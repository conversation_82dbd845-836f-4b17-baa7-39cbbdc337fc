# 自由相机控制功能指南

## 概述

本项目现在支持强大的自由相机控制功能，允许您在3D场景中精确控制相机的位置和朝向。

## 功能特性

### 1. 基础控制
- **键盘控制**：使用WASD键移动，QE键上下移动，方向键旋转视角
- **鼠标控制**：左键拖拽旋转视角，支持指针锁定模式
- **平滑移动**：支持平滑过渡到目标位置和角度

### 2. 精确位置控制
- **数值输入**：直接输入X、Y、Z坐标精确设置相机位置
- **角度控制**：通过俯仰角和偏航角精确控制相机朝向
- **看向目标**：指定目标点，相机自动朝向该位置

### 3. 预设位置
提供多个预设相机位置：
- 🏠 **默认位置**：标准观察角度
- 🚗 **车辆视角**：最佳车辆观察角度
- 🌅 **侧面视角**：从侧面观察场景
- 🔝 **俯视角度**：从上方俯视整个场景
- 👁️ **近距离**：近距离观察细节

### 4. 高级功能
- **围绕目标旋转**：以指定点为中心进行轨道运动
- **鼠标灵敏度调节**：可调节鼠标控制的灵敏度
- **平滑动画**：所有移动和旋转都支持平滑过渡效果

## 使用方法

### 启用自由相机
1. 在右侧控制面板中找到"📷 自由相机控制"
2. 勾选"启用自由相机"复选框
3. 相机控制面板将展开显示所有控制选项

### 键盘控制
- **W/S**：前进/后退
- **A/D**：左移/右移  
- **Q/E**：上升/下降
- **↑/↓**：俯仰角度调整
- **←/→**：水平旋转

### 鼠标控制
1. 确保"启用鼠标控制"已勾选
2. 左键点击并拖拽来旋转视角
3. 左键点击可激活指针锁定模式，获得更好的控制体验

### 精确位置设置
1. 在"精确位置控制"区域输入目标坐标
2. 点击"📍 应用位置"按钮或按Enter键
3. 相机将移动到指定位置（支持平滑移动）

### 精确朝向设置
1. 在"精确朝向控制"区域输入俯仰角和偏航角（以度为单位）
2. 点击"🎯 应用朝向"按钮或按Enter键
3. 相机将旋转到指定角度

### 看向目标功能
1. 在"看向目标"区域输入目标点坐标
2. 点击"👁️ 看向目标"按钮
3. 相机将自动调整朝向，面向指定目标点

### 使用预设位置
1. 在"预设位置"区域选择任意预设按钮
2. 相机将平滑移动到预设位置和角度
3. 每个预设都有描述提示，鼠标悬停可查看

## 配置选项

### 移动和旋转速度
- **移动速度**：调节键盘移动的速度（1-20）
- **旋转速度**：调节键盘旋转的速度

### 鼠标控制
- **鼠标灵敏度**：调节鼠标旋转的灵敏度（0.5-5.0）
- **启用鼠标控制**：开关鼠标控制功能

### 平滑移动
- **平滑移动**：启用位置变化的平滑过渡
- **平滑旋转**：启用角度变化的平滑过渡

### 视野设置
- **FOV（视野角度）**：调节相机的视野范围（30°-120°）
- **水平角度范围**：限制水平旋转范围
- **俯仰角度范围**：限制垂直旋转范围

## 实用技巧

1. **快速定位**：使用预设位置快速跳转到常用视角
2. **精确调整**：使用数值输入进行精确的位置和角度调整
3. **平滑体验**：启用平滑移动获得更好的视觉体验
4. **鼠标控制**：在需要快速调整视角时启用鼠标控制
5. **看向功能**：使用看向目标功能快速对准感兴趣的物体

## 注意事项

- 启用自由相机时，轨道控制器将被禁用
- 鼠标控制可能会锁定指针，按ESC键可退出锁定模式
- 平滑移动会有短暂的动画时间，请耐心等待完成
- 角度输入使用度数制，范围为-90°到90°（俯仰角）

## API接口

如果您需要通过代码控制相机，可以使用以下方法：

```javascript
// 获取ThreeManager实例
const threeManager = threeSceneRef.value?.getThreeManager()

// 设置相机位置
threeManager.setFreeCameraPosition(x, y, z, smooth)

// 设置相机旋转
threeManager.setFreeCameraRotation(x, y, z, smooth)

// 看向目标点
threeManager.freeCameraLookAt(x, y, z, smooth)

// 应用预设
threeManager.applyFreeCameraPreset(preset, smooth)

// 围绕目标旋转
threeManager.freeCameraOrbitTarget(targetX, targetY, targetZ, radius, azimuth, elevation, smooth)
```

这些增强的自由相机控制功能为您提供了完全的3D场景导航自由度，无论是进行精确的技术观察还是创造性的视角探索都能满足需求。
