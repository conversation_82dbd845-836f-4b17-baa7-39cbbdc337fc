# 自由相机FOV视野角度控制功能指南

## 🎯 功能概述

新增的FOV（Field of View）控制功能允许您根据水平和垂直视角角度来动态调整自由相机的视野范围，模拟真实相机的拍摄逻辑。这个功能基于三角函数计算，提供精确的视野控制和距离计算。

## ✨ 核心功能

### 1. 视角角度控制
- **水平视角**：10° - 120° 可调节
- **垂直视角**：5° - 90° 可调节
- **实时更新**：调整时立即生效

### 2. 长宽比计算
- **自动计算**：基于水平和垂直视角自动计算长宽比
- **实时显示**：以 "X.XX:1" 格式显示
- **数学原理**：长宽比 = tan(水平角/2) / tan(垂直角/2)

### 3. 距离计算
- **目标尺寸输入**：可设置目标物体的宽度和高度（cm）
- **多种计算方式**：
  - 基于宽度的距离计算
  - 基于高度的距离计算
  - 建议距离（综合计算）

### 4. 真实相机模拟
- **物理准确**：基于真实光学原理
- **实用计算**：适用于实际拍摄场景规划

## 🎛️ 用户界面

### 视野角度控制区域
```
视野角度控制:
水平视角: [====●====] 60°
垂直视角: [===●=====] 30°
长宽比: 2.00:1

目标尺寸 (用于距离计算):
宽度 (cm): [20.0]  高度 (cm): [10.0]

建议距离: 18.66 cm
基于宽度: 17.32 cm
基于高度: 18.66 cm
```

### 控制元素说明
1. **水平视角滑块**：控制水平方向的视野角度
2. **垂直视角滑块**：控制垂直方向的视野角度
3. **长宽比显示**：实时显示计算出的视野长宽比
4. **目标尺寸输入**：设置要拍摄的目标物体尺寸
5. **距离计算结果**：显示建议的拍摄距离

## 🔬 技术原理

### 数学计算公式

#### 长宽比计算
```
aspectRatio = tan(horizontalFOV/2) / tan(verticalFOV/2)
```

#### 距离计算
```
基于宽度: distance = (targetWidth/2) / tan(horizontalFOV/2)
基于高度: distance = (targetHeight/2) / tan(verticalFOV/2)
建议距离: distance = (基于宽度 + 基于高度) / 2
```

#### FOV转换
```
Three.js使用垂直FOV，所以：
camera.fov = verticalFOV
```

### 实际应用示例

#### 示例1：标准拍摄
- **目标**：拍摄20cm×10cm的物体
- **水平视角**：60°
- **垂直视角**：30°
- **计算结果**：
  - 长宽比：2.00:1
  - 建议距离：18.66cm

#### 示例2：广角拍摄
- **目标**：拍摄30cm×20cm的物体
- **水平视角**：90°
- **垂直视角**：60°
- **计算结果**：
  - 长宽比：1.73:1
  - 建议距离：15.00cm

#### 示例3：长焦拍摄
- **目标**：拍摄10cm×5cm的物体
- **水平视角**：30°
- **垂直视角**：15°
- **计算结果**：
  - 长宽比：2.00:1
  - 建议距离：18.66cm

## 🎮 使用方法

### 基本操作流程

1. **启用自由相机模式**
   ```
   勾选 "启用自由相机"
   ```

2. **设置视角参数**
   ```
   调整水平视角滑块 (10° - 120°)
   调整垂直视角滑块 (5° - 90°)
   ```

3. **输入目标尺寸**
   ```
   设置目标宽度 (cm)
   设置目标高度 (cm)
   ```

4. **查看计算结果**
   ```
   观察长宽比显示
   查看建议距离
   参考不同计算方式的结果
   ```

5. **应用到相机**
   ```
   FOV设置自动应用到自由相机
   可切换到自由相机模式查看效果
   ```

### 实际应用场景

#### 产品拍摄规划
1. 测量产品尺寸
2. 设置期望的视角角度
3. 根据计算结果确定拍摄距离
4. 调整相机位置到建议距离

#### 建筑摄影规划
1. 测量建筑物关键尺寸
2. 根据构图需求设置视角
3. 计算最佳拍摄距离
4. 规划拍摄位置

#### 微距摄影
1. 设置较小的视角角度
2. 输入微小物体尺寸
3. 获得精确的近距离拍摄参数

## 🔧 高级功能

### 动态FOV调整
- **实时预览**：调整参数时立即看到效果
- **平滑过渡**：FOV变化有平滑动画
- **精确控制**：1度精度的角度控制

### 多种计算模式
- **基于宽度**：适用于横向构图
- **基于高度**：适用于纵向构图
- **综合建议**：平衡两种计算的结果

### 参数同步
- **配置保存**：FOV设置与相机配置同步
- **状态维持**：切换模式时保持FOV设置
- **事件通知**：参数变化时通知其他组件

## 💡 使用技巧

### 1. 视角选择建议
- **广角 (>70°)**：适合大场景、建筑摄影
- **标准 (40°-70°)**：适合人像、产品摄影
- **长焦 (<40°)**：适合远距离、细节拍摄

### 2. 长宽比应用
- **16:9 (1.78:1)**：视频标准比例
- **4:3 (1.33:1)**：传统照片比例
- **3:2 (1.50:1)**：35mm胶片比例
- **1:1**：正方形构图

### 3. 距离计算优化
- **优先使用建议距离**：综合考虑了宽度和高度
- **根据构图调整**：横构图参考宽度，竖构图参考高度
- **考虑安全距离**：实际拍摄时留出操作空间

### 4. 实际应用建议
- **先设置目标尺寸**：确保计算准确性
- **逐步调整视角**：观察长宽比变化
- **验证实际效果**：切换到自由相机模式确认
- **记录理想参数**：保存常用的FOV设置

## 🎨 视觉效果

### FOV对视觉的影响
- **小FOV (窄视角)**：类似长焦镜头，视野窄，透视压缩
- **大FOV (广视角)**：类似广角镜头，视野宽，透视夸张
- **标准FOV**：接近人眼视角，自然透视

### 长宽比的视觉意义
- **高长宽比 (>2:1)**：宽屏效果，适合风景
- **标准长宽比 (1.5-2:1)**：平衡构图
- **低长宽比 (<1.5:1)**：接近正方形，紧凑构图

这个FOV控制功能为自由相机系统增加了专业级的视野控制能力，让您能够像使用真实相机一样精确控制视野范围和拍摄参数。
