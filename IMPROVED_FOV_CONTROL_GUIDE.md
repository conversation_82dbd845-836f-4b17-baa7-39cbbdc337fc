# 改进的FOV视野角度控制功能

## 🎯 功能概述

现在的FOV控制功能支持**独立调整水平和垂直视角**，真正模拟了专业相机的视野控制逻辑。用户可以分别设置水平FOV和垂直FOV，系统会自动计算并应用正确的长宽比。

## ✨ 核心改进

### 1. 独立的水平和垂直FOV控制
- **水平视角 (H-FOV)**：10° - 120° 独立调节
- **垂直视角 (V-FOV)**：5° - 90° 独立调节
- **真实相机模拟**：就像调整真实相机的焦距和传感器比例

### 2. 智能长宽比计算
- **自动计算**：基于水平和垂直FOV自动计算实际长宽比
- **数学精确**：使用 `tan(H-FOV/2) / tan(V-FOV/2)` 公式
- **实时更新**：调整任一FOV时立即重新计算

### 3. 等效焦距显示
- **35mm等效**：显示等效35mm胶片的焦距
- **专业参考**：便于摄影师理解视角效果
- **实时计算**：基于垂直FOV计算等效焦距

### 4. 视觉区分设计
- **颜色编码**：水平FOV滑块为橙红色，垂直FOV滑块为绿色
- **清晰标识**：H-FOV和V-FOV标签明确区分
- **直观操作**：一目了然的控制界面

## 🎛️ 用户界面

### 改进后的FOV控制区域
```
视野角度控制:
水平视角 (H-FOV): [====●====] 60° (橙红色滑块)
垂直视角 (V-FOV): [===●=====] 30° (绿色滑块)

计算长宽比: 2.00:1
等效焦距: 43mm (35mm等效)
[🔄 重置FOV到默认值]

目标尺寸 (用于距离计算):
宽度 (cm): [20.0]  高度 (cm): [10.0]

建议距离: 18.66 cm
基于宽度: 17.32 cm
基于高度: 18.66 cm
```

## 🔬 技术实现

### FOV应用逻辑
```javascript
// 1. 设置垂直FOV到Three.js相机
camera.fov = verticalFOV

// 2. 计算基于FOV的长宽比
const horizontalRad = horizontalFOV * Math.PI / 180
const verticalRad = verticalFOV * Math.PI / 180
const fovBasedAspect = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)

// 3. 应用计算出的长宽比
camera.aspect = fovBasedAspect
camera.updateProjectionMatrix()
```

### 等效焦距计算
```javascript
// 基于35mm胶片垂直尺寸(24mm)计算等效焦距
const verticalRad = verticalFOV * Math.PI / 180
const focalLength = (24 / 2) / Math.tan(verticalRad / 2)
```

## 🎮 使用方法

### 基本操作

1. **启用自由相机**
   ```
   勾选 "启用自由相机"
   ```

2. **调整水平视角**
   ```
   拖动橙红色的H-FOV滑块 (10° - 120°)
   控制水平方向的视野范围
   ```

3. **调整垂直视角**
   ```
   拖动绿色的V-FOV滑块 (5° - 90°)
   控制垂直方向的视野范围
   ```

4. **观察计算结果**
   ```
   查看实时计算的长宽比
   参考等效焦距值
   ```

5. **重置到默认值**
   ```
   点击 "重置FOV到默认值" 按钮
   恢复到 H-FOV:60°, V-FOV:30°
   ```

### 实际应用场景

#### 1. 宽屏视频拍摄
- **设置**：H-FOV: 90°, V-FOV: 30°
- **效果**：3.00:1 超宽屏比例
- **用途**：电影级宽屏效果

#### 2. 标准摄影
- **设置**：H-FOV: 60°, V-FOV: 40°
- **效果**：1.50:1 标准比例
- **用途**：常规摄影构图

#### 3. 人像摄影
- **设置**：H-FOV: 40°, V-FOV: 30°
- **效果**：1.33:1 人像比例
- **用途**：人像特写拍摄

#### 4. 建筑摄影
- **设置**：H-FOV: 100°, V-FOV: 75°
- **效果**：1.33:1 广角效果
- **用途**：建筑全景拍摄

## 🎨 视觉效果对比

### 不同FOV组合的效果

#### 超广角效果
- **H-FOV: 120°, V-FOV: 90°**
- 长宽比: 1.33:1
- 等效焦距: 12mm
- 效果: 极度广角，透视夸张

#### 广角效果
- **H-FOV: 90°, V-FOV: 60°**
- 长宽比: 1.73:1
- 等效焦距: 20mm
- 效果: 广角视野，适合风景

#### 标准效果
- **H-FOV: 60°, V-FOV: 30°**
- 长宽比: 2.00:1
- 等效焦距: 43mm
- 效果: 接近人眼视角

#### 长焦效果
- **H-FOV: 30°, V-FOV: 20°**
- 长宽比: 1.50:1
- 等效焦距: 69mm
- 效果: 长焦压缩，适合人像

#### 超长焦效果
- **H-FOV: 15°, V-FOV: 10°**
- 长宽比: 1.50:1
- 等效焦距: 137mm
- 效果: 强烈压缩，远距离拍摄

## 💡 专业使用技巧

### 1. 根据构图需求选择比例
- **横向构图**：增大H-FOV，适度调整V-FOV
- **纵向构图**：增大V-FOV，适度调整H-FOV
- **方形构图**：保持H-FOV和V-FOV接近1:1比例

### 2. 模拟不同镜头效果
- **鱼眼镜头**：H-FOV > 100°, V-FOV > 80°
- **广角镜头**：H-FOV: 70-100°, V-FOV: 50-70°
- **标准镜头**：H-FOV: 40-70°, V-FOV: 30-50°
- **长焦镜头**：H-FOV: 15-40°, V-FOV: 10-30°

### 3. 距离计算优化
- **精确测量目标尺寸**：确保计算准确性
- **考虑实际约束**：物理空间限制
- **验证实际效果**：切换到自由相机查看

### 4. 创意应用
- **电影宽屏**：H-FOV: 90°, V-FOV: 25° (3.6:1)
- **全景效果**：H-FOV: 120°, V-FOV: 40° (3:1)
- **微距效果**：H-FOV: 20°, V-FOV: 15° (1.33:1)

## 🔧 技术优势

### 1. 真实相机模拟
- 独立控制水平和垂直FOV
- 精确的长宽比计算
- 专业的等效焦距参考

### 2. 实时响应
- 滑块调整立即生效
- 计算结果实时更新
- 平滑的视觉过渡

### 3. 专业工具
- 35mm等效焦距显示
- 精确的距离计算
- 多种预设和重置功能

这个改进的FOV控制系统为自由相机提供了专业级的视野控制能力，让您能够像使用真实相机一样精确控制视野范围和画面比例。
