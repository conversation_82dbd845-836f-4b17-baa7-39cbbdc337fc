# 🎯 精确视野控制指南

## 📋 功能概述

新的精确视野控制系统确保自由相机的视野范围与设定的水平和垂直视场角完全一致，避免任何视野畸变。

## ✨ 主要改进

### 1. 精确FOV模式
- **启用时**：视野严格按照设定的H-FOV和V-FOV显示，确保无畸变
- **禁用时**：视野适配屏幕比例，可能产生轻微畸变以适应显示器

### 2. 视野匹配状态显示
- **完美匹配**：设定比例与屏幕比例完全一致
- **精确匹配**：轻微差异但在可接受范围内
- **强制精确**：强制使用设定比例，忽略屏幕比例

### 3. 长宽比计算优化
使用精确的正切函数计算：
```javascript
const preciseAspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)
```

## 🎮 使用方法

### 基本操作

1. **启用自由相机**
   ```
   勾选 "启用自由相机"
   ```

2. **设置视场角**
   ```
   水平视角 (H-FOV): 10° - 120°
   垂直视角 (V-FOV): 5° - 90°
   ```

3. **启用精确FOV模式**
   ```
   勾选 "精确FOV模式"
   ```

### 实际应用示例

#### 示例1：1:1 正方形视野
- 水平FOV: 60°
- 垂直FOV: 60°
- 结果：完美的1:1长宽比视野

#### 示例2：16:9 宽屏视野
- 水平FOV: 90°
- 垂直FOV: 51°
- 结果：接近16:9的宽屏效果

#### 示例3：4:3 传统视野
- 水平FOV: 60°
- 垂直FOV: 45°
- 结果：4:3的传统屏幕比例

## 🔧 技术实现

### 精确长宽比计算
```javascript
// 计算基于用户设定的H-FOV和V-FOV的精确长宽比
const horizontalRad = horizontalFOV * Math.PI / 180
const verticalRad = verticalFOV * Math.PI / 180

// 使用正切函数计算精确的视野长宽比
const preciseAspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)

// 应用到Three.js相机
camera.fov = verticalFOV
camera.aspect = preciseAspectRatio
camera.updateProjectionMatrix()
```

### 畸变检测
```javascript
// 计算屏幕比例与设定比例的差异
const screenAspect = window.innerWidth / window.innerHeight
const fovAspect = Math.tan(hFOV * Math.PI / 360) / Math.tan(vFOV * Math.PI / 360)
const distortion = Math.abs(screenAspect - fovAspect) / fovAspect
```

## 📊 状态指示器

### 视野匹配度
- 🟢 **完美匹配** (差异 < 1%)
- 🔵 **精确匹配** (差异 < 5%)  
- 🟣 **强制精确** (强制使用设定比例)

### 畸变程度
- 🟢 **无畸变** (差异 < 5%)
- 🟠 **轻微畸变** (差异 5-15%)
- 🔴 **中等畸变** (差异 15-30%)
- ⚫ **严重畸变** (差异 > 30%)

## 💡 使用建议

### 何时使用精确FOV模式
- ✅ 需要精确的视野控制
- ✅ 进行专业摄影或测量
- ✅ 要求无畸变的视觉效果
- ✅ 特定长宽比的内容创作

### 何时使用屏幕适配模式
- ✅ 一般浏览和观察
- ✅ 适应不同屏幕尺寸
- ✅ 优先考虑显示效果
- ✅ 不关心精确的视野角度

## 🚀 快速设置

### 常用视野预设
1. **标准视野**: H-FOV 60°, V-FOV 30° (2:1)
2. **正方形视野**: H-FOV 60°, V-FOV 60° (1:1)
3. **宽屏视野**: H-FOV 90°, V-FOV 51° (16:9)
4. **超宽视野**: H-FOV 110°, V-FOV 40° (2.75:1)

### 调试模式
开发环境下会在控制台输出详细的FOV信息：
- 请求的FOV值
- 实际应用的FOV值
- 长宽比信息
- 畸变检测结果

## 🔍 故障排除

### 常见问题

**Q: 为什么视野看起来被拉伸了？**
A: 可能是精确FOV模式下，设定的长宽比与屏幕比例差异较大。可以：
- 调整H-FOV和V-FOV使其比例接近屏幕比例
- 或者禁用精确FOV模式使用屏幕适配

**Q: 如何获得完美的1:1视野？**
A: 设置相同的H-FOV和V-FOV值，例如都设为60°，并启用精确FOV模式。

**Q: 视野角度不准确怎么办？**
A: 确保启用了精确FOV模式，系统会强制使用设定的精确角度值。
