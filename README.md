# ZK Demo - 3D车辆场景演示

这是一个基于Vue3和Three.js的3D车辆场景演示项目，支持通过电文控制车辆移动。

## 功能特性

- ✅ Vue3 + TypeScript + Vite 开发环境
- ✅ Three.js 3D场景渲染
- ✅ 白色光照环境
- ✅ 车辆GLB模型加载支持
- ✅ 电文触发车辆移动
- ✅ 履带控制系统（以履带中心点旋转）
- ✅ 实时车辆控制面板
- ✅ 预设移动命令
- ✅ 平滑动画过渡

## 项目结构

```
src/
├── components/
│   └── ThreeScene.vue          # 3D场景组件
├── utils/
│   ├── ThreeManager.ts         # Three.js场景管理器
│   ├── VehicleController.ts    # 车辆控制器
│   ├── MessageHandler.ts       # 电文处理器
│   └── ModelLoader.ts          # 模型加载器
├── App.vue                     # 主应用组件
├── main.ts                     # 应用入口
└── style.css                   # 全局样式
```

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 打开浏览器访问：http://localhost:5174

## 电文格式

### 移动到指定位置
```json
{
  "vehicleId": "car1",
  "action": "move",
  "x": 10,
  "y": 0,
  "z": 5,
  "speed": 2
}
```

### 按方向移动
```json
{
  "vehicleId": "car1",
  "action": "move",
  "direction": "forward",
  "distance": 5,
  "speed": 2
}
```

### 旋转车辆
```json
{
  "vehicleId": "car1",
  "action": "turn",
  "direction": "left",
  "angle": 45,
  "speed": 1
}
```

### 重置位置
```json
{
  "vehicleId": "car1",
  "action": "reset"
}
```

### 履带控制
```json
{
  "vehicleId": "car1",
  "action": "track",
  "trackAction": "start",
  "trackNames": ["履带1", "履带2", "履带3"],
  "rotationSpeed": 2
}
```

**履带控制说明：**
- 履带会以自己的几何中心为轴进行Y轴旋转
- 系统自动计算每个履带的包围盒中心点
- 支持单个或多个履带的独立控制
- trackAction: "start" | "stop" | "setSpeed"

## 支持的参数

- **vehicleId**: 车辆ID（必需）
- **action**: 动作类型（move, turn, reset）
- **x, y, z**: 目标坐标
- **direction**: 方向（forward, backward, left, right）
- **distance**: 移动距离
- **angle**: 旋转角度（度）
- **speed**: 移动/旋转速度

## 场景控制

- 鼠标左键拖拽：旋转视角
- 鼠标滚轮：缩放
- 鼠标右键拖拽：平移视角

## GLB模型支持

项目支持加载GLB格式的车辆模型。如果没有提供GLB模型，系统会自动使用内置的简单车辆模型。

要加载自定义GLB模型，可以调用：
```typescript
threeManager.loadCustomVehicle('path/to/your/model.glb', 'vehicleId')
```

## 技术栈

- **Vue 3**: 前端框架
- **TypeScript**: 类型安全
- **Three.js**: 3D图形渲染
- **Vite**: 构建工具

## 开发说明

1. 场景使用白色光照，包含环境光和方向光
2. 车辆移动使用平滑插值动画
3. 支持多车辆管理（通过vehicleId区分）
4. 电文处理支持格式验证
5. 模型加载支持错误回退机制

## 构建部署

```bash
npm run build
```

构建产物将生成在 `dist` 目录中。
