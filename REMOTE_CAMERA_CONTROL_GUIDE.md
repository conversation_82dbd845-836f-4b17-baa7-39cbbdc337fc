# 远程自由相机控制功能使用指南

## 功能概述

现在您可以在轨道相机模式下直接控制自由相机的位置和朝向，无需切换到自由相机模式。这样您可以在轨道相机视角下观察自由相机（红色球体标识）的实时移动。

## 主要特性

✅ **在轨道相机模式下远程控制自由相机**  
✅ **实时观察自由相机位置变化**  
✅ **独立的按键控制，不与轨道相机冲突**  
✅ **精确的数值显示和手动输入**  
✅ **预设位置快速跳转**  

## 使用步骤

### 1. 启用远程控制
1. 在右侧控制面板找到 **"🎮 远程自由相机控制"**
2. 勾选 **"启用远程控制"** 复选框
3. 现在您可以使用键盘控制自由相机

### 2. 键盘控制

#### 位置控制（WASD + QE）：
- **W** - 自由相机前进
- **S** - 自由相机后退  
- **A** - 自由相机左移
- **D** - 自由相机右移
- **Q** - 自由相机上升
- **E** - 自由相机下降

#### 朝向控制（方向键）：
- **←** - 自由相机左转
- **→** - 自由相机右转
- **↑** - 自由相机上仰
- **↓** - 自由相机下俯

### 3. 观察效果
- 在轨道相机视角下，您可以看到红色球体（自由相机标识）
- 使用键盘控制时，红色球体会实时移动
- 在控制面板中可以看到精确的位置和朝向数值

## 界面功能

### 实时状态显示
- **位置**：显示自由相机的 X、Y、Z 坐标（实时更新）
- **朝向**：显示俯仰角和偏航角（以度为单位）

### 快速操作按钮
- **🔄 重置位置** - 将自由相机重置到默认位置 (10, 10, 10)
- **📷 跳转到自由相机视角** - 切换到自由相机模式查看效果
- **📋 复制当前位置** - 复制位置和朝向信息到剪贴板
- **👁️ 隐藏/显示标识** - 控制红色球体的显示/隐藏

### 预设位置
快速跳转到常用的自由相机位置：
- **🏠 默认** - 标准位置 (10, 10, 10)
- **🚗 车辆上方** - 车辆正上方俯视
- **🌅 车辆前方** - 车辆前方观察
- **🔍 近距离** - 近距离观察细节
- **🌄 远景** - 远距离全景观察
- **🎬 电影视角** - 电影般的观察角度

### 手动精确设置
- **位置输入** - 直接输入 X、Y、Z 坐标
- **朝向输入** - 直接输入俯仰角和偏航角（度）
- **应用按钮** - 立即应用设置的数值

## 最佳工作流程

1. **启用远程控制** - 勾选"启用远程控制"
2. **粗调位置** - 使用 WASD+QE 键大致移动自由相机到目标区域
3. **调整朝向** - 使用方向键调整自由相机的朝向
4. **精确调整** - 使用手动输入进行精确的位置和角度调整
5. **查看效果** - 点击"跳转到自由相机视角"切换查看
6. **保存位置** - 使用"复制当前位置"保存理想设置

## 实用技巧

### 快速定位
- 使用预设位置快速跳转到常用角度
- 先用预设接近目标，再用键盘微调

### 精确控制
- 观察实时数值显示进行精确控制
- 使用手动输入设置精确的坐标和角度

### 效果预览
- 在轨道相机下调整完成后，切换到自由相机查看最终效果
- 可以在两种模式间快速切换对比

### 位置记录
- 使用"复制当前位置"功能记录理想的相机设置
- 可以将位置信息保存到文档中供后续使用

## 注意事项

1. **键盘焦点** - 确保浏览器窗口处于活动状态
2. **按键冲突** - 远程控制使用 WASD+QE 和方向键，与轨道相机的鼠标控制不冲突
3. **角度限制** - 俯仰角被限制在 ±72° 范围内，防止过度旋转
4. **实时更新** - 位置和朝向数值会实时更新，便于精确控制

## 故障排除

### 按键不响应
- 点击 3D 场景区域确保获得键盘焦点
- 检查"启用远程控制"是否已勾选

### 自由相机标识不可见
- 检查"显示标识"按钮状态
- 调整轨道相机角度查看不同方向
- 确保自由相机位置在合理范围内

### 数值不更新
- 刷新页面重新初始化
- 检查浏览器控制台是否有错误信息

这个功能大大提升了相机调试的效率，让您能够在保持全局视角的同时精确控制自由相机的位置和朝向。
