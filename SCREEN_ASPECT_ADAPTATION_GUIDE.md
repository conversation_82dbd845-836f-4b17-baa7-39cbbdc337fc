# 屏幕长宽比适配功能指南

## 🎯 问题解决

### 原始问题
当屏幕的长宽比与H-FOV和V-FOV计算出的长宽比不匹配时，会出现视野畸变。这是因为Three.js相机的aspect ratio与实际显示区域的aspect ratio不一致导致的。

### 解决方案
新增了智能屏幕长宽比适配系统，可以自动检测屏幕比例并提供多种适配模式来消除视野畸变。

## ✨ 核心功能

### 1. 自动屏幕检测
- **实时监测**：自动检测当前屏幕/窗口的长宽比
- **动态更新**：窗口大小变化时自动重新计算
- **精确显示**：显示当前屏幕长宽比（如 1.78:1）

### 2. 畸变程度评估
- **无畸变**：长宽比差异 < 5%（绿色显示）
- **轻微畸变**：长宽比差异 5-15%（橙色显示）
- **中等畸变**：长宽比差异 15-30%（红橙色显示）
- **严重畸变**：长宽比差异 > 30%（红色显示）

### 3. 多种适配模式
- **适应屏幕 (Fit)**：保持内容完整，可能有黑边
- **填充屏幕 (Fill)**：填满屏幕，可能裁剪内容
- **拉伸适配 (Stretch)**：强制匹配屏幕比例
- **裁剪适配 (Crop)**：保持原始比例，裁剪超出部分

### 4. 优先轴选择
- **水平优先**：优先保持水平FOV不变
- **垂直优先**：优先保持垂直FOV不变
- **自动选择**：根据屏幕比例自动选择最佳轴

## 🎛️ 用户界面

### 控制面板位置
在"📷 相机控制" Tab的FOV信息显示区域中新增了"长宽比适配"部分：

```
视野角度控制:
水平视角 (H-FOV): [====●====] 60°
垂直视角 (V-FOV): [===●=====] 30°

计算长宽比: 2.00:1
屏幕长宽比: 1.78:1  ← 新增
等效焦距: 43mm (35mm等效)

长宽比适配:  ← 新增区域
☑️ 启用屏幕适配
适配模式: [适应屏幕 ▼]
优先轴: [自动选择 ▼]

实际H-FOV: 53.1°
实际V-FOV: 30.0°
畸变程度: 轻微畸变
```

### 控制选项详解

#### 启用屏幕适配
- **关闭**：使用原始的H-FOV和V-FOV设置
- **开启**：根据屏幕比例自动调整FOV

#### 适配模式选择
1. **适应屏幕 (Fit)**
   - 保持内容完整显示
   - 可能在屏幕边缘出现黑边
   - 适合需要看到完整视野的场景

2. **填充屏幕 (Fill)**
   - 填满整个屏幕
   - 可能裁剪部分内容
   - 适合沉浸式体验

3. **拉伸适配 (Stretch)**
   - 强制匹配屏幕长宽比
   - 可能产生轻微变形
   - 适合对比例要求不严格的场景

4. **裁剪适配 (Crop)**
   - 保持原始FOV比例
   - 裁剪超出屏幕的部分
   - 适合需要保持原始视角的场景

#### 优先轴选择
- **水平优先**：保持水平FOV不变，调整垂直FOV
- **垂直优先**：保持垂直FOV不变，调整水平FOV
- **自动选择**：根据屏幕与FOV的比例关系自动选择

## 🔬 技术原理

### 畸变产生原因
```javascript
// 屏幕长宽比
screenAspect = screenWidth / screenHeight

// FOV计算的长宽比
fovAspect = tan(H-FOV/2) / tan(V-FOV/2)

// 当 screenAspect ≠ fovAspect 时产生畸变
distortion = |screenAspect - fovAspect| / fovAspect
```

### 适配算法

#### Fit模式（适应屏幕）
```javascript
if (screenAspect > fovAspect) {
  // 屏幕更宽，调整水平FOV
  actualH-FOV = 2 * atan(tan(V-FOV/2) * screenAspect)
  actualV-FOV = V-FOV
} else {
  // 屏幕更高，调整垂直FOV
  actualH-FOV = H-FOV
  actualV-FOV = 2 * atan(tan(H-FOV/2) / screenAspect)
}
```

#### Fill模式（填充屏幕）
```javascript
if (screenAspect < fovAspect) {
  // 屏幕更窄，扩展水平FOV
  actualH-FOV = 2 * atan(tan(V-FOV/2) * screenAspect)
  actualV-FOV = V-FOV
} else {
  // 屏幕更宽，扩展垂直FOV
  actualH-FOV = H-FOV
  actualV-FOV = 2 * atan(tan(H-FOV/2) / screenAspect)
}
```

#### Stretch模式（拉伸适配）
```javascript
// 强制使用屏幕长宽比
actualH-FOV = 2 * atan(tan(V-FOV/2) * screenAspect)
actualV-FOV = V-FOV
```

## 🎮 使用方法

### 基本使用流程

1. **设置基础FOV**
   ```
   设置期望的H-FOV和V-FOV值
   例如：H-FOV: 60°, V-FOV: 30°
   ```

2. **检查畸变程度**
   ```
   查看"畸变程度"显示
   如果显示"严重畸变"，建议启用适配
   ```

3. **启用屏幕适配**
   ```
   勾选"启用屏幕适配"
   选择合适的适配模式
   ```

4. **调整适配参数**
   ```
   根据需要选择适配模式和优先轴
   观察"实际H-FOV"和"实际V-FOV"的变化
   ```

5. **验证效果**
   ```
   切换到自由相机模式查看实际效果
   确认畸变是否已消除
   ```

### 不同场景的推荐设置

#### 标准显示器（16:9）
- **屏幕比例**：1.78:1
- **推荐FOV**：H-FOV: 53°, V-FOV: 30°
- **适配模式**：Fit或Stretch
- **优先轴**：垂直优先

#### 超宽显示器（21:9）
- **屏幕比例**：2.33:1
- **推荐FOV**：H-FOV: 70°, V-FOV: 30°
- **适配模式**：Fill或Stretch
- **优先轴**：垂直优先

#### 方形显示器（4:3）
- **屏幕比例**：1.33:1
- **推荐FOV**：H-FOV: 40°, V-FOV: 30°
- **适配模式**：Fit
- **优先轴**：水平优先

#### 竖屏显示（9:16）
- **屏幕比例**：0.56:1
- **推荐FOV**：H-FOV: 17°, V-FOV: 30°
- **适配模式**：Fill
- **优先轴**：垂直优先

## 💡 使用技巧

### 1. 快速消除畸变
- 启用屏幕适配
- 选择"适应屏幕"模式
- 设置为"自动选择"优先轴

### 2. 保持原始视角
- 使用"裁剪适配"模式
- 接受可能的内容裁剪
- 保持FOV比例不变

### 3. 沉浸式体验
- 使用"填充屏幕"模式
- 选择合适的优先轴
- 获得无黑边的全屏体验

### 4. 精确控制
- 手动设置H-FOV和V-FOV
- 使用"拉伸适配"模式
- 通过实际FOV值验证效果

## 🔧 技术优势

### 1. 自动化处理
- 无需手动计算长宽比
- 自动检测屏幕变化
- 实时调整相机参数

### 2. 多种适配策略
- 满足不同使用场景
- 灵活的优先级设置
- 可视化的效果预览

### 3. 无缝集成
- 与现有FOV系统完美结合
- 不影响其他相机功能
- 保持向后兼容性

这个屏幕长宽比适配系统彻底解决了视野畸变问题，为用户提供了专业级的相机控制体验！
