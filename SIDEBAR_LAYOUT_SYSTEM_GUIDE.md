# 新侧边栏布局系统指南

## 🎯 系统概述

全新设计的侧边栏布局系统提供了现代化的用户界面，将所有控制功能整合到一个可收起展开的侧边栏中，并通过Tab切换来组织不同的功能模块。

## ✨ 核心特性

### 1. 可收起展开的侧边栏
- **汉堡菜单按钮**：位于侧边栏右上角的动画按钮
- **收起状态**：宽度60px，只显示Tab图标
- **展开状态**：宽度320px，显示完整控制面板
- **平滑动画**：使用CSS cubic-bezier缓动函数

### 2. 多Tab切换界面
- **📷 相机控制**：自由相机和全局相机设置
- **🎮 远程控制**：远程自由相机控制
- **🎛️ 视野设置**：H-FOV和V-FOV控制
- **⚙️ 系统设置**：性能、显示、控制等配置

### 3. 响应式设计
- **100vh高度**：占满整个视口高度
- **自适应布局**：3D场景区域自动调整
- **移动端优化**：小屏幕下的特殊处理

### 4. 状态记忆功能
- **localStorage保存**：记住展开/收起状态
- **Tab状态保存**：记住当前活跃的Tab
- **用户偏好**：下次访问时恢复设置

## 🎛️ Tab功能详解

### 📷 相机控制 Tab
包含两个主要组件：

#### 自由相机控制
- 启用/禁用自由相机
- 移动速度和旋转速度调整
- 鼠标控制设置（默认关闭）
- 平滑移动和旋转选项
- 相机球体和方向指示线显示控制
- 位置和旋转的手动设置
- 相机预设位置

#### 全局相机控制
- 启用/禁用全局相机
- 重置到全局视角
- 俯视图和等轴测图快速切换
- 全局相机配置选项

### 🎮 远程控制 Tab
专门的远程自由相机控制：

- **键盘控制**：WASD+QE位置控制，方向键朝向控制
- **实时状态显示**：位置坐标和朝向角度
- **快速操作**：重置、跳转、复制位置、显示控制
- **预设位置**：6个常用相机位置
- **手动设置**：精确的位置和朝向输入
- **距离计算**：基于目标尺寸的拍摄距离建议

### 🎛️ 视野设置 Tab
专业的FOV控制系统：

#### H-FOV和V-FOV控制
- **水平视角**：10°-120°，橙红色滑块
- **垂直视角**：5°-90°，绿色滑块
- **实时计算**：长宽比和等效焦距
- **重置功能**：一键恢复默认设置

#### FOV预设
- **🎬 电影**：90°×25° (3.6:1)
- **📺 标准**：60°×30° (2:1)
- **👤 人像**：40°×30° (1.33:1)
- **🏢 建筑**：100°×75° (1.33:1)
- **📱 竖屏**：30°×50° (0.6:1)
- **🌄 全景**：110°×40° (2.75:1)

#### 距离计算
- **目标尺寸输入**：宽度和高度（cm）
- **多种计算方式**：基于宽度、高度、综合建议
- **实时更新**：调整FOV时自动重新计算

#### 精确设置
- **手动输入**：直接输入H-FOV和V-FOV数值
- **范围限制**：自动限制在有效范围内
- **一键应用**：立即应用设置

### ⚙️ 系统设置 Tab
综合的系统配置：

#### 🚀 性能设置
- **垂直同步**：启用/禁用VSync
- **渲染质量**：0.5x-2.0x渲染分辨率
- **阴影质量**：低/中/高/超高

#### 🖥️ 显示设置
- **FPS计数器**：显示/隐藏帧率
- **网格显示**：显示/隐藏参考网格
- **坐标轴**：显示/隐藏XYZ轴
- **背景颜色**：自定义场景背景色

#### 🎮 控制设置
- **鼠标Y轴反转**：适应不同操作习惯
- **鼠标灵敏度**：0.1-5.0倍调节
- **键盘移动速度**：1-20倍速度调节

#### 💾 存储设置
- **自动保存**：自动保存所有设置
- **记住相机位置**：保存相机状态
- **导出/导入**：设置的备份和恢复
- **重置功能**：恢复所有默认设置

#### 🐛 调试设置
- **调试模式**：启用开发者功能
- **控制台日志**：显示详细日志
- **线框模式**：显示3D模型线框

#### ℹ️ 系统信息
- **浏览器信息**：当前浏览器类型
- **WebGL版本**：图形API版本
- **GPU信息**：显卡型号
- **屏幕分辨率**：当前显示分辨率

#### 🚗 车辆控制（集成）
- **电文输入**：车辆移动命令
- **移动控制**：前进、后退、转向、重置
- **演示场景**：预设的演示动作
- **履带控制**：启动/停止、速度调节

## 🎨 视觉设计

### 色彩系统
- **侧边栏背景**：黑色半透明 + 毛玻璃效果
- **Tab导航**：渐变底部指示器
- **相机控制**：绿色主题 (#4CAF50)
- **远程控制**：紫色主题 (#9C27B0)
- **FOV控制**：蓝色主题 (#3F51B5)
- **系统设置**：白色主题

### 动画效果
- **侧边栏展开/收起**：0.3s cubic-bezier缓动
- **Tab切换**：透明度和位移过渡
- **汉堡菜单**：旋转和淡入淡出动画
- **按钮悬停**：颜色和变形过渡

### 响应式布局
- **桌面端**：320px侧边栏，60px收起状态
- **平板端**：280px侧边栏，50px收起状态
- **手机端**：全屏侧边栏，40px收起状态

## 🔧 技术实现

### 组件架构
```
SidebarContainer (主容器)
├── Tab Navigation (导航栏)
├── Tab Content Area (内容区域)
│   ├── CameraControls + GlobalCameraControls
│   ├── RemoteFreeCameraControls
│   ├── FOVControls
│   └── SystemSettings + VehicleControls
└── Collapsed Indicator (收起状态指示器)
```

### 状态管理
- **Vue 3 Composition API**：响应式状态管理
- **localStorage**：持久化用户偏好
- **Props/Events**：组件间通信
- **Expose API**：父组件控制接口

### 性能优化
- **v-show指令**：避免重复渲染
- **CSS transitions**：硬件加速动画
- **懒加载**：按需加载Tab内容
- **事件防抖**：优化频繁操作

## 💡 使用技巧

### 快速操作
1. **快速展开**：点击收起状态的Tab图标
2. **键盘快捷键**：可扩展键盘快捷键支持
3. **拖拽调整**：未来可支持侧边栏宽度调整

### 工作流程建议
1. **相机设置**：先在"相机控制"Tab设置基本参数
2. **视野调整**：在"视野设置"Tab精确调整FOV
3. **远程操作**：在"远程控制"Tab进行精细调整
4. **系统优化**：在"系统设置"Tab优化性能和显示

### 自定义配置
- **导出设置**：保存当前所有配置
- **导入设置**：在不同设备间同步配置
- **重置选项**：快速恢复默认状态

这个新的侧边栏系统提供了现代化、专业化的用户界面，大大提升了操作效率和用户体验！
