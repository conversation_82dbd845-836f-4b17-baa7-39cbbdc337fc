# 🎯 视野框架系统使用指南

## 📋 功能概述

视野框架系统通过在相机前方添加一个矩形框架和灰色蒙板，解决了在16:9屏幕上显示1:1视野时的画面拉伸问题。

## ✨ 核心特性

### 1. 精确视野比例
- 🎯 **矩形框架**：显示精确的FOV范围边界
- 🌫️ **灰色蒙板**：遮挡超出设定视野的区域
- 📐 **比例保持**：确保视野比例与设定的H-FOV/V-FOV完全一致

### 2. 动态尺寸计算
- 📏 **水平角度**：角度越大，框架宽度越大
- 📐 **垂直角度**：角度越大，框架高度越大
- 🔄 **实时更新**：框架尺寸随FOV设置实时变化

### 3. 智能蒙板系统
- 🎭 **自适应遮挡**：只在需要时显示蒙板
- 🔍 **精确计算**：基于屏幕比例和FOV比例的差异
- 🎨 **可调透明度**：蒙板透明度可自定义

## 🎮 使用方法

### 基本操作

1. **启用自由相机**
   ```
   勾选 "启用自由相机"
   ```

2. **设置视场角**
   ```
   水平视角: 60°
   垂直视角: 60°  (1:1 正方形视野)
   ```

3. **启用视野框架**
   ```
   勾选 "显示视野框架"
   ```

### 配置选项

#### 框架距离 (2-20)
- **作用**：控制框架距离相机的距离
- **建议值**：5-10 (太近会遮挡视野，太远会影响精度)

#### 蒙板透明度 (0-100%)
- **作用**：控制灰色蒙板的透明度
- **建议值**：30% (既能看到被遮挡区域，又能明确区分视野边界)

#### 显示框架边界
- **作用**：是否显示白色的框架边界线
- **用途**：帮助精确定位视野范围

## 🔧 技术原理

### 框架尺寸计算
```javascript
// 基于FOV角度和距离计算框架实际尺寸
const distance = 5  // 框架距离
const horizontalRad = horizontalFOV * Math.PI / 180
const verticalRad = verticalFOV * Math.PI / 180

const width = 2 * distance * Math.tan(horizontalRad / 2)
const height = 2 * distance * Math.tan(verticalRad / 2)
```

### 蒙板生成逻辑
```javascript
// 计算屏幕在框架距离处的尺寸
const screenVerticalFOV = camera.fov * Math.PI / 180
const screenHeight = 2 * distance * Math.tan(screenVerticalFOV / 2)
const screenWidth = screenHeight * screenAspect

// 如果框架小于屏幕，创建蒙板遮挡多余区域
if (frameWidth < screenWidth || frameHeight < screenHeight) {
  // 创建左右/上下蒙板
}
```

## 📊 实际应用示例

### 示例1：1:1 正方形视野 (16:9屏幕)
- **设置**：H-FOV 60°, V-FOV 60°
- **效果**：中央显示1:1正方形区域，左右两侧有灰色蒙板
- **用途**：正方形内容创作、对称构图

### 示例2：4:3 传统视野 (16:9屏幕)
- **设置**：H-FOV 60°, V-FOV 45°
- **效果**：中央显示4:3矩形区域，左右两侧有灰色蒙板
- **用途**：传统摄影比例、老式显示器模拟

### 示例3：2.35:1 电影宽屏 (16:9屏幕)
- **设置**：H-FOV 90°, V-FOV 38°
- **效果**：中央显示超宽屏区域，上下有灰色蒙板
- **用途**：电影级宽屏效果、影视创作

## 🎨 视觉效果

### 框架显示
- **颜色**：白色边框 (可配置)
- **透明度**：80% (可配置)
- **样式**：细线框架，不遮挡内容

### 蒙板显示
- **颜色**：黑色/灰色 (可配置)
- **透明度**：30% (可配置)
- **效果**：半透明遮挡，仍可看到被遮挡内容

## 💡 使用技巧

### 最佳实践
1. **框架距离**：设置为5-8，既不会太近影响观察，也不会太远影响精度
2. **蒙板透明度**：30-50%，既能明确区分边界，又不会完全遮挡
3. **边界显示**：初次使用时开启，熟悉后可关闭以减少干扰

### 性能优化
- 框架系统使用轻量级几何体，对性能影响极小
- 蒙板只在需要时创建，避免不必要的渲染开销
- 实时更新采用高效的矩阵变换，确保流畅体验

## 🔍 故障排除

### 常见问题

**Q: 框架不显示怎么办？**
A: 检查是否启用了"显示视野框架"，确保自由相机处于激活状态。

**Q: 蒙板太暗看不清被遮挡区域？**
A: 降低蒙板透明度到20-30%，或者临时关闭蒙板查看全景。

**Q: 框架位置不准确？**
A: 调整框架距离，建议设置为5-10之间的值。

**Q: 性能有影响吗？**
A: 框架系统经过优化，对性能影响极小，可以放心使用。

## 🚀 高级功能

### 自定义配置
- 框架颜色可通过配置修改
- 蒙板颜色支持自定义
- 透明度支持实时调整

### 开发者模式
- 控制台输出详细的框架信息
- 实时显示框架尺寸和位置
- 性能监控和调试信息

这个视野框架系统完美解决了不同屏幕比例下的视野显示问题，让你能够获得真正精确的视野控制体验！
