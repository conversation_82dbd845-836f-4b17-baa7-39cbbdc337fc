# 矿山布料机演示场景

## 直行拐弯演示场景

这是一个完整的矿山布料机操作演示，展示了履带启动、直行、转弯、停止的完整流程。

### 演示流程

1. **重置位置** - 将车辆重置到原点
2. **启动履带** - 开始履带旋转
3. **直行前进** - 向前移动10米
4. **右转90度** - 模拟避障或路径调整
5. **继续直行** - 向前移动8米
6. **左转45度** - 精确角度调整
7. **最终直行** - 到达目标位置
8. **停止履带** - 完成操作

### 电文序列

#### 1. 重置位置
```json
{
  "vehicleId": "car1",
  "action": "reset"
}
```

#### 2. 启动履带（2秒后）
```json
{
  "vehicleId": "car1",
  "action": "track",
  "trackAction": "start",
  "trackNames": ["履带1", "履带2", "履带3"],
  "rotationSpeed": 2
}
```

#### 3. 直行前进10米（4秒后）
```json
{
  "vehicleId": "car1",
  "action": "move",
  "direction": "forward",
  "distance": 10,
  "speed": 3
}
```

#### 4. 右转90度（8秒后）
```json
{
  "vehicleId": "car1",
  "action": "turn",
  "direction": "right",
  "angle": 90,
  "speed": 1
}
```

#### 5. 继续直行8米（10秒后）
```json
{
  "vehicleId": "car1",
  "action": "move",
  "direction": "forward",
  "distance": 8,
  "speed": 3
}
```

#### 6. 左转45度（14秒后）
```json
{
  "vehicleId": "car1",
  "action": "turn",
  "direction": "left",
  "angle": 45,
  "speed": 1
}
```

#### 7. 最终直行5米（16秒后）
```json
{
  "vehicleId": "car1",
  "action": "move",
  "direction": "forward",
  "distance": 5,
  "speed": 2
}
```

#### 8. 停止履带（20秒后）
```json
{
  "vehicleId": "car1",
  "action": "track",
  "trackAction": "stop"
}
```

## 其他演示场景

### 场景2：精确定位
模拟布料机到达指定坐标点的精确定位操作。

```json
{
  "vehicleId": "car1",
  "action": "move",
  "x": 15,
  "y": 0,
  "z": 10,
  "speed": 2
}
```

### 场景3：差速转向
通过控制不同履带实现差速转向。

```json
{
  "vehicleId": "car1",
  "action": "track",
  "trackAction": "start",
  "trackNames": ["履带1", "履带3"],
  "rotationSpeed": 3
}
```

### 场景4：慢速精确操作
在狭窄空间内的慢速精确移动。

```json
{
  "vehicleId": "car1",
  "action": "move",
  "direction": "forward",
  "distance": 2,
  "speed": 0.5
}
```

## 使用说明

1. **自动演示**：点击"直行拐弯演示"按钮，系统会自动执行完整的演示序列
2. **手动控制**：复制上述电文到输入框，手动发送控制命令
3. **实时观察**：通过3D场景观察车辆的移动和履带旋转效果
4. **参数调整**：可以修改速度、距离、角度等参数来适应不同需求

## 技术特点

- **时序控制**：精确的时间间隔确保动作顺序执行
- **履带同步**：履带旋转与车辆移动同步进行
- **平滑过渡**：所有动作都有平滑的动画过渡
- **状态反馈**：控制台输出详细的执行状态信息

## 实际应用场景

这个演示模拟了矿山布料机在实际工作中的典型操作：
- 从停车位置启动
- 沿预定路径行驶
- 遇到障碍物时转向
- 到达工作区域后精确定位
- 完成作业后停止设备
