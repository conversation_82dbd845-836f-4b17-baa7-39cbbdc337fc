{"name": "zkdemo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-check": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@types/three": "^0.178.1", "three": "^0.178.0", "vue": "^3.4.0"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^5.0.0", "typescript": "^4.9.5", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}}