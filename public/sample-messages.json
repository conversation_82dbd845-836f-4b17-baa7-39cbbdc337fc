{"examples": {"moveToPosition": {"description": "移动到指定坐标", "message": {"vehicleId": "car1", "action": "move", "x": 10, "y": 0, "z": 5, "speed": 2}}, "moveForward": {"description": "向前移动", "message": {"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 10, "speed": 3}}, "moveBackward": {"description": "向后移动", "message": {"vehicleId": "car1", "action": "move", "direction": "backward", "distance": 5, "speed": 2}}, "turnLeft": {"description": "左转45度", "message": {"vehicleId": "car1", "action": "turn", "direction": "left", "angle": 45, "speed": 1}}, "turnRight": {"description": "右转90度", "message": {"vehicleId": "car1", "action": "turn", "direction": "right", "angle": 90, "speed": 1.5}}, "complexMovement": {"description": "复杂移动序列", "sequence": [{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 15, "speed": 2}, {"vehicleId": "car1", "action": "turn", "direction": "right", "angle": 90, "speed": 1}, {"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 10, "speed": 2}]}, "resetPosition": {"description": "重置到原点", "message": {"vehicleId": "car1", "action": "reset"}}}, "templates": {"basicMove": {"vehicleId": "car1", "action": "move", "x": 0, "y": 0, "z": 0, "speed": 2}, "basicTurn": {"vehicleId": "car1", "action": "turn", "direction": "left", "angle": 45, "speed": 1}}}