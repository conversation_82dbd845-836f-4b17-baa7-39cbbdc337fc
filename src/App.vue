<template>
  <div class="app">
    <!-- Sidebar Container -->
    <SidebarContainer ref="sidebarRef" :defaultTab="'camera'" :rememberState="true">
      <!-- Camera Controls Tab -->
      <template #camera-controls>
        <CameraControls
          ref="cameraControlsRef"
          :onToggleCamera="handleToggleCamera"
          :onConfigUpdate="handleCameraConfigUpdate"
          :onJumpToCamera="handleJumpToCamera"
          :onResetCamera="handleResetCamera"
          :onToggleVisibility="handleToggleCameraVisibility"
          :onSetPosition="handleSetCameraPosition"
          :onSetRotation="handleSetCameraRotation"
          :onLookAt="handleCameraLookAt"
          :onApplyPreset="handleApplyCameraPreset"
          :onToggleCameraSphere="handleToggleCameraSphere"
          :onToggleDirectionIndicator="handleToggleDirectionIndicator"
          :onUpdateFOV="handleUpdateCameraFOV"
          :onSetPrecisionMode="handleSetCameraPrecisionMode"
          :onSetViewportFrame="handleSetViewportFrame"
          :onUpdateViewportFrameConfig="handleUpdateViewportFrameConfig"
        />

        <GlobalCameraControls
          ref="globalCameraControlsRef"
          :onToggleGlobalCamera="handleToggleGlobalCamera"
          :onConfigUpdate="handleGlobalCameraConfigUpdate"
          :onResetToGlobalView="handleResetToGlobalView"
          :onSetTopDownView="handleSetTopDownView"
          :onSetIsometricView="handleSetIsometricView"
        />
      </template>

      <!-- Remote Controls Tab -->
      <template #remote-controls>
        <RemoteFreeCameraControls
          ref="remoteFreeCameraControlsRef"
          :onToggleRemoteControl="handleToggleRemoteFreeCameraControl"
          :onResetFreeCameraPosition="handleResetFreeCamera"
          :onJumpToFreeCameraView="handleJumpToFreeCameraView"
          :onToggleFreeCameraVisibility="handleToggleFreeCameraVisibility"
          :onApplyFreeCameraPreset="handleApplyFreeCameraPreset"
          :onSetFreeCameraPosition="handleSetFreeCameraPosition"
          :onSetFreeCameraRotation="handleSetFreeCameraRotation"
        />
      </template>

      <!-- FOV Controls Tab -->
      <template #fov-controls>
        <FOVControls
          ref="fovControlsRef"
          :onUpdateFOV="handleUpdateCameraFOV"
        />
      </template>

      <!-- System Settings Tab -->
      <template #system-settings>
        <SystemSettings
          ref="systemSettingsRef"
          :onSettingsChange="handleSystemSettingsChange"
        />

        <!-- Vehicle Controls in System Settings -->
        <div class="vehicle-controls-section">
          <h5>🚗 车辆控制</h5>
          <div class="message-input">
            <label>电文输入:</label>
            <textarea
              v-model="messageText"
              placeholder="输入车辆移动电文，格式: {vehicleId: 'car1', x: 10, y: 0, z: 5, speed: 2}"
              rows="4"
            ></textarea>
            <button @click="sendMessage">发送电文</button>
          </div>
          <div class="preset-commands">
            <h6>移动命令:</h6>
            <button @click="moveForward">前进</button>
            <button @click="moveBackward">后退</button>
            <button @click="turnLeft">左转</button>
            <button @click="turnRight">右转</button>
            <button @click="resetPosition">重置位置</button>
          </div>

          <div class="demo-scenarios">
            <h6>演示场景:</h6>
            <button @click="demonstrateStraightAndTurn" class="demo-btn">直行拐弯演示</button>
            <button @click="loadDemoMessage" class="demo-btn">加载演示电文</button>
            <div v-if="demoStatus" class="demo-status">
              <span class="status-text">{{ demoStatus }}</span>
            </div>
          </div>

          <div class="track-commands">
            <h6>履带控制:</h6>
            <button @click="startTracks" :class="{ active: isTracksRotating }">启动履带</button>
            <button @click="stopTracks" :class="{ inactive: !isTracksRotating }">停止履带</button>
            <div class="speed-control">
              <label>履带速度:</label>
              <input
                type="range"
                v-model="trackSpeed"
                min="0.1"
                max="5"
                step="0.1"
                @input="setTrackSpeed"
              />
              <span>{{ trackSpeed }}</span>
            </div>
          </div>
        </div>
      </template>
    </SidebarContainer>

    <!-- 3D Scene Container -->
    <div class="scene-container" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <ThreeScene ref="threeSceneRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import * as THREE from 'three'
import ThreeScene from './components/ThreeScene.vue'
import SidebarContainer from './components/SidebarContainer.vue'
import CameraControls from './components/CameraControls.vue'
import GlobalCameraControls from './components/GlobalCameraControls.vue'
import RemoteFreeCameraControls from './components/RemoteFreeCameraControls.vue'
import FOVControls from './components/FOVControls.vue'
import SystemSettings from './components/SystemSettings.vue'
import { messageHandler } from './utils/MessageHandler'
import { CameraConfig, CameraPreset } from './types/CameraTypes'
import { GlobalCameraConfig } from './utils/GlobalCamera'

const messageText = ref('')
const trackSpeed = ref(2)
const isTracksRotating = ref(false)
const demoStatus = ref('')

// Component references
const sidebarRef = ref<InstanceType<typeof SidebarContainer> | null>(null)
const cameraControlsRef = ref<InstanceType<typeof CameraControls> | null>(null)
const globalCameraControlsRef = ref<InstanceType<typeof GlobalCameraControls> | null>(null)
const remoteFreeCameraControlsRef = ref<InstanceType<typeof RemoteFreeCameraControls> | null>(null)
const fovControlsRef = ref<InstanceType<typeof FOVControls> | null>(null)
const systemSettingsRef = ref<InstanceType<typeof SystemSettings> | null>(null)

// Sidebar state
const sidebarCollapsed = computed(() => {
  return sidebarRef.value?.isCollapsed || false
})

// Three scene reference to access ThreeManager
const threeSceneRef = ref<InstanceType<typeof ThreeScene> | null>(null)

const sendMessage = () => {
  try {
    const message = JSON.parse(messageText.value)
    messageHandler.processMessage(message)
    messageText.value = ''
  } catch (error) {
    console.error('电文格式错误:', error)
    alert('电文格式错误，请检查JSON格式')
  }
}

const moveForward = () => {
  messageHandler.processMessage({
    vehicleId: 'car1',
    action: 'move',
    direction: 'forward',
    distance: 5,
    speed: 2
  })
}

const moveBackward = () => {
  messageHandler.processMessage({
    vehicleId: 'car1',
    action: 'move',
    direction: 'backward',
    distance: 5,
    speed: 2
  })
}

const turnLeft = () => {
  messageHandler.processMessage({
    vehicleId: 'car1',
    action: 'turn',
    direction: 'left',
    angle: 45,
    speed: 1
  })
}

const turnRight = () => {
  messageHandler.processMessage({
    vehicleId: 'car1',
    action: 'turn',
    direction: 'right',
    angle: 45,
    speed: 1
  })
}

const resetPosition = () => {
  messageHandler.processMessage({
    vehicleId: 'car1',
    action: 'reset'
  })
}

// 履带控制方法
const startTracks = () => {
  messageHandler.processMessage({
    vehicleId: 'car1',
    action: 'track',
    trackAction: 'start',
    trackNames: ['履带1', '履带2', '履带3'],
    rotationSpeed: trackSpeed.value
  })
  isTracksRotating.value = true
}

const stopTracks = () => {
  messageHandler.processMessage({
    vehicleId: 'car1',
    action: 'track',
    trackAction: 'stop'
  })
  isTracksRotating.value = false
}

const setTrackSpeed = () => {
  if (isTracksRotating.value) {
    messageHandler.processMessage({
      vehicleId: 'car1',
      action: 'track',
      trackAction: 'setSpeed',
      rotationSpeed: trackSpeed.value
    })
  }
}

// 演示场景方法
const demonstrateStraightAndTurn = () => {
  demoStatus.value = '🔄 准备演示...'

  // 重置位置
  messageHandler.processMessage({
    vehicleId: 'car1',
    action: 'reset'
  })

  // 延时执行演示序列
  setTimeout(() => {
    console.log('开始直行拐弯演示...')
    demoStatus.value = '🚀 启动履带...'

    // 1. 启动履带
    messageHandler.processMessage({
      vehicleId: 'car1',
      action: 'track',
      trackAction: 'start',
      trackNames: ['履带1', '履带2', '履带3'],
      rotationSpeed: 2
    })
    isTracksRotating.value = true

    // 2. 直行10米 (2秒后)
    setTimeout(() => {
      demoStatus.value = '➡️ 直行前进 10米...'
      messageHandler.processMessage({
        vehicleId: 'car1',
        action: 'move',
        direction: 'forward',
        distance: 10,
        speed: 3
      })
    }, 2000)

    // 3. 右转90度 (6秒后)
    setTimeout(() => {
      demoStatus.value = '🔄 右转 90度...'
      messageHandler.processMessage({
        vehicleId: 'car1',
        action: 'turn',
        direction: 'right',
        angle: 90,
        speed: 1
      })
    }, 6000)

    // 4. 继续直行8米 (8秒后)
    setTimeout(() => {
      demoStatus.value = '➡️ 继续直行 8米...'
      messageHandler.processMessage({
        vehicleId: 'car1',
        action: 'move',
        direction: 'forward',
        distance: 8,
        speed: 3
      })
    }, 8000)

    // 5. 左转45度 (12秒后)
    setTimeout(() => {
      demoStatus.value = '🔄 左转 45度...'
      messageHandler.processMessage({
        vehicleId: 'car1',
        action: 'turn',
        direction: 'left',
        angle: 45,
        speed: 1
      })
    }, 12000)

    // 6. 最后直行5米 (14秒后)
    setTimeout(() => {
      demoStatus.value = '🎯 到达目标位置...'
      messageHandler.processMessage({
        vehicleId: 'car1',
        action: 'move',
        direction: 'forward',
        distance: 5,
        speed: 2
      })
    }, 14000)

    // 7. 停止履带 (18秒后)
    setTimeout(() => {
      demoStatus.value = '✅ 演示完成！'
      messageHandler.processMessage({
        vehicleId: 'car1',
        action: 'track',
        trackAction: 'stop'
      })
      isTracksRotating.value = false
      console.log('直行拐弯演示完成！')

      // 3秒后清除状态
      setTimeout(() => {
        demoStatus.value = ''
      }, 3000)
    }, 18000)

  }, 500)
}

const loadDemoMessage = () => {
  const demoMessages = [
    {
      name: '直行移动',
      message: {
        vehicleId: 'car1',
        action: 'move',
        direction: 'forward',
        distance: 15,
        speed: 2.5
      }
    },
    {
      name: '精确定位',
      message: {
        vehicleId: 'car1',
        action: 'move',
        x: 10,
        y: 0,
        z: 8,
        speed: 2
      }
    },
    {
      name: '启动履带',
      message: {
        vehicleId: 'car1',
        action: 'track',
        trackAction: 'start',
        trackNames: ['履带1', '履带2', '履带3'],
        rotationSpeed: 2
      }
    },
    {
      name: '右转90度',
      message: {
        vehicleId: 'car1',
        action: 'turn',
        direction: 'right',
        angle: 90,
        speed: 1
      }
    }
  ]

  // 随机选择一个演示电文
  const randomDemo = demoMessages[Math.floor(Math.random() * demoMessages.length)]
  messageText.value = JSON.stringify(randomDemo.message, null, 2)

  console.log(`加载演示电文: ${randomDemo.name}`)
}

// Camera control handlers
const handleToggleCamera = (active: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.toggleFreeCamera(active)

    // Update camera controls UI with current camera state
    if (cameraControlsRef.value && active) {
      const freeCamera = threeManager.getFreeCamera()
      const state = freeCamera.getState()
      const config = freeCamera.getConfig()

      cameraControlsRef.value.updatePosition(state.position.x, state.position.y, state.position.z)
      cameraControlsRef.value.updateRotation(state.rotation.x, state.rotation.y, state.rotation.z)
      cameraControlsRef.value.setConfig(config)
    }
  }
}

const handleCameraConfigUpdate = (config: Partial<CameraConfig>) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.updateFreeCameraConfig(config)
  }
}

const handleJumpToCamera = () => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.jumpToFreeCamera()
  }
}

const handleResetCamera = () => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.resetFreeCamera()

    // Update UI
    if (cameraControlsRef.value) {
      cameraControlsRef.value.updatePosition(10, 10, 10)
      cameraControlsRef.value.updateRotation(0, 0, 0)
    }
  }
}

const handleToggleCameraVisibility = () => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.toggleFreeCameraVisibility()
  }
}

// 新增的相机控制处理函数
const handleSetCameraPosition = (x: number, y: number, z: number, smooth: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setFreeCameraPosition(x, y, z, smooth)
  }
}

const handleSetCameraRotation = (x: number, y: number, z: number, smooth: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setFreeCameraRotation(x, y, z, smooth)
  }
}

const handleCameraLookAt = (x: number, y: number, z: number, smooth: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.freeCameraLookAt(x, y, z, smooth)
  }
}

const handleApplyCameraPreset = (preset: CameraPreset, smooth: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.applyFreeCameraPreset(preset, smooth)
  }
}

// 自由相机显示控制处理函数
const handleToggleCameraSphere = (visible: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setFreeCameraSphereVisible(visible)
  }
}

const handleToggleDirectionIndicator = (visible: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setFreeCameraDirectionVisible(visible)
  }
}

// FOV控制处理函数
const handleUpdateCameraFOV = (horizontalFOV: number, verticalFOV: number, aspectRatio: number) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.updateFreeCameraFOV(horizontalFOV, verticalFOV, aspectRatio)

    // 同时更新自由相机控制UI的状态
    if (cameraControlsRef.value) {
      // 通知UI组件FOV已更新
      console.log(`FOV updated: H-FOV=${horizontalFOV}°, V-FOV=${verticalFOV}°, Aspect=${aspectRatio.toFixed(2)}:1`)
    }
  }
}

const handleSetCameraPrecisionMode = (enabled: boolean, forceAspectRatio: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setFreeCameraPrecisionMode(enabled, forceAspectRatio)

    console.log(`Camera Precision Mode: ${enabled ? 'Enabled' : 'Disabled'}${enabled ? ` (Force Aspect: ${forceAspectRatio})` : ''}`)
  }
}

const handleSetViewportFrame = (enabled: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setFreeCameraViewportFrame(enabled)

    console.log(`Viewport Frame: ${enabled ? 'Enabled' : 'Disabled'}`)
  }
}

const handleUpdateViewportFrameConfig = (config: any) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.updateFreeCameraViewportFrameConfig(config)

    console.log('Viewport Frame Config Updated:', config)
  }
}

// 屏幕适配处理函数
const handleScreenAdaptation = (enabled: boolean, mode: string, priority: string) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setFreeCameraScreenAdaptation(enabled, mode, priority)
  }
}

// 窗口大小变化处理
const handleWindowResize = () => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.updateFreeCameraScreenAspect()
  }

  // 通知相机控制组件更新屏幕尺寸
  if (cameraControlsRef.value) {
    cameraControlsRef.value.updateScreenDimensions()
  }
}

// 系统设置处理函数
const handleSystemSettingsChange = (category: string, settings: any) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (!threeManager) return

  switch (category) {
    case 'performance':
      // 处理性能设置
      if (settings.renderQuality !== undefined) {
        // 更新渲染质量
        const renderer = threeManager.getRenderer()
        const canvas = renderer.domElement
        const pixelRatio = window.devicePixelRatio * settings.renderQuality
        renderer.setPixelRatio(Math.min(pixelRatio, 2))
      }
      break

    case 'display':
      // 处理显示设置
      if (settings.backgroundColor !== undefined) {
        const scene = threeManager.getScene()
        scene.background = new THREE.Color(settings.backgroundColor)
      }
      if (settings.showGrid !== undefined) {
        // 显示/隐藏网格
        threeManager.toggleGrid(settings.showGrid)
      }
      if (settings.showAxes !== undefined) {
        // 显示/隐藏坐标轴
        threeManager.toggleAxes(settings.showAxes)
      }
      break

    case 'control':
      // 处理控制设置
      if (settings.mouseSensitivity !== undefined) {
        threeManager.setFreeCameraMouseSensitivity(settings.mouseSensitivity * 0.002)
      }
      break

    case 'debug':
      // 处理调试设置
      if (settings.enableDebugMode !== undefined) {
        // 启用/禁用调试模式
        console.log('Debug mode:', settings.enableDebugMode)
      }
      if (settings.showWireframe !== undefined) {
        // 显示/隐藏线框模式
        threeManager.toggleWireframe(settings.showWireframe)
      }
      break
  }
}

// Global camera control handlers
const handleToggleGlobalCamera = (active: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.toggleGlobalCamera(active)

    // Disable free camera if global camera is activated
    if (active && cameraControlsRef.value) {
      cameraControlsRef.value.isCameraActive = false
    }

    // Update global camera controls UI
    if (globalCameraControlsRef.value && active) {
      const globalCamera = threeManager.getGlobalCamera()
      const config = globalCamera.getConfig()
      const position = globalCamera.getPosition()

      globalCameraControlsRef.value.setConfig(config)
      globalCameraControlsRef.value.updatePosition(position.x, position.y, position.z)
    }
  }
}

const handleGlobalCameraConfigUpdate = (config: Partial<GlobalCameraConfig>) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.updateGlobalCameraConfig(config)
  }
}

const handleResetToGlobalView = () => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.resetToGlobalView()

    // Update UI
    if (globalCameraControlsRef.value) {
      globalCameraControlsRef.value.updatePosition(0, 50, 0)
    }
  }
}

const handleSetTopDownView = () => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setTopDownView()
  }
}

const handleSetIsometricView = () => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.setIsometricView()
  }
}



// 远程自由相机控制处理函数
const handleToggleRemoteFreeCameraControl = (enabled: boolean) => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    threeManager.enableRemoteFreeCameraControl(enabled)

    // 更新UI状态
    if (remoteFreeCameraControlsRef.value) {
      remoteFreeCameraControlsRef.value.setRemoteControlEnabled(enabled)
    }
  }
}

const handleJumpToFreeCameraView = () => {
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    // 切换到自由相机模式
    threeManager.toggleFreeCamera(true)

    // 更新自由相机控制UI
    if (cameraControlsRef.value) {
      const freeCameraState = threeManager.getFreeCamera().getState()
      cameraControlsRef.value.updatePosition(freeCameraState.position.x, freeCameraState.position.y, freeCameraState.position.z)
      cameraControlsRef.value.updateRotation(freeCameraState.rotation.x, freeCameraState.rotation.y, freeCameraState.rotation.z)
    }
  }
}

// Event listeners for camera updates
let cameraPositionListener: ((event: CustomEvent) => void) | null = null
let cameraRotationListener: ((event: CustomEvent) => void) | null = null
let globalCameraPositionListener: ((event: CustomEvent) => void) | null = null
let remoteFreeCameraUpdateListener: ((event: CustomEvent) => void) | null = null

onMounted(() => {
  // 添加窗口大小变化监听器
  window.addEventListener('resize', handleWindowResize)

  // Set up event listeners for camera updates
  const setupCameraListeners = () => {
    const threeManager = threeSceneRef.value?.getThreeManager()
    if (threeManager) {
      const container = threeManager.getContainer()

      cameraPositionListener = (event: CustomEvent) => {
        if (cameraControlsRef.value) {
          const { x, y, z } = event.detail
          cameraControlsRef.value.updatePosition(x, y, z)
        }
      }

      cameraRotationListener = (event: CustomEvent) => {
        if (cameraControlsRef.value) {
          const { x, y, z } = event.detail
          cameraControlsRef.value.updateRotation(x, y, z)
        }
      }

      globalCameraPositionListener = (event: CustomEvent) => {
        if (globalCameraControlsRef.value) {
          const { x, y, z } = event.detail
          globalCameraControlsRef.value.updatePosition(x, y, z)
        }
      }



      remoteFreeCameraUpdateListener = (event: CustomEvent) => {
        if (remoteFreeCameraControlsRef.value) {
          const { position, rotation } = event.detail
          remoteFreeCameraControlsRef.value.updateFreeCameraPosition(position.x, position.y, position.z)
          remoteFreeCameraControlsRef.value.updateFreeCameraRotation(rotation.x, rotation.y, rotation.z)
        }
      }

      container?.addEventListener('cameraPositionChange', cameraPositionListener as EventListener)
      container?.addEventListener('cameraRotationChange', cameraRotationListener as EventListener)
      container?.addEventListener('globalCameraPositionChange', globalCameraPositionListener as EventListener)
      container?.addEventListener('remoteFreeCameraUpdate', remoteFreeCameraUpdateListener as EventListener)
    }
  }

  // Delay setup to ensure ThreeManager is initialized
  setTimeout(setupCameraListeners, 100)
})

onUnmounted(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleWindowResize)

  // Clean up event listeners
  const threeManager = threeSceneRef.value?.getThreeManager()
  if (threeManager) {
    const container = threeManager.getContainer()
    if (cameraPositionListener) {
      container?.removeEventListener('cameraPositionChange', cameraPositionListener as EventListener)
    }
    if (cameraRotationListener) {
      container?.removeEventListener('cameraRotationChange', cameraRotationListener as EventListener)
    }
    if (globalCameraPositionListener) {
      container?.removeEventListener('globalCameraPositionChange', globalCameraPositionListener as EventListener)
    }
  }
})
</script>

<style scoped>
.app {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.scene-container {
  position: absolute;
  top: 0;
  left: 320px; /* Default sidebar width */
  right: 0;
  bottom: 0;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scene-container.sidebar-collapsed {
  left: 60px; /* Collapsed sidebar width */
}

/* Vehicle Controls Styles */
.vehicle-controls-section {
  background: rgba(76, 175, 80, 0.1);
  padding: 12px;
  border-radius: 6px;
  margin-top: 16px;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.vehicle-controls-section h5 {
  margin: 0 0 10px 0;
  color: #4CAF50;
  font-size: 12px;
  font-weight: bold;
}

.vehicle-controls-section h6 {
  margin: 8px 0 6px 0;
  color: #66BB6A;
  font-size: 10px;
  font-weight: bold;
}

.controls h3 {
  margin-bottom: 15px;
  color: #4CAF50;
}

.message-input {
  margin-bottom: 20px;
}

.message-input label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.message-input textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #333;
  color: white;
  margin-bottom: 10px;
  resize: vertical;
}

.message-input button {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.message-input button:hover {
  background: #45a049;
}

.preset-commands h4 {
  margin-bottom: 10px;
  color: #2196F3;
}

.preset-commands button {
  background: #2196F3;
  color: white;
  border: none;
  padding: 6px 12px;
  margin: 2px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.preset-commands button:hover {
  background: #1976D2;
}

.track-commands {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #444;
}

.track-commands h4 {
  margin-bottom: 10px;
  color: #FF9800;
}

.track-commands button {
  background: #FF9800;
  color: white;
  border: none;
  padding: 6px 12px;
  margin: 2px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.track-commands button:hover {
  background: #F57C00;
}

.track-commands button.active {
  background: #4CAF50;
}

.track-commands button.active:hover {
  background: #45a049;
}

.track-commands button.inactive {
  background: #666;
  opacity: 0.6;
}

.speed-control {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.speed-control label {
  font-size: 12px;
  color: #ccc;
}

.speed-control input[type="range"] {
  flex: 1;
  max-width: 100px;
}

.speed-control span {
  font-size: 12px;
  color: #FF9800;
  font-weight: bold;
  min-width: 30px;
}

.demo-scenarios {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #444;
}

.demo-scenarios h4 {
  margin-bottom: 10px;
  color: #9C27B0;
}

.demo-scenarios .demo-btn {
  background: linear-gradient(45deg, #9C27B0, #E91E63);
  color: white;
  border: none;
  padding: 8px 12px;
  margin: 2px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
}

.demo-scenarios .demo-btn:hover {
  background: linear-gradient(45deg, #7B1FA2, #C2185B);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(156, 39, 176, 0.4);
}

.demo-scenarios .demo-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
}

.demo-status {
  margin-top: 10px;
  padding: 8px 12px;
  background: rgba(156, 39, 176, 0.1);
  border: 1px solid rgba(156, 39, 176, 0.3);
  border-radius: 4px;
  text-align: center;
}

.demo-status .status-text {
  color: #E1BEE7;
  font-size: 12px;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}
</style>
