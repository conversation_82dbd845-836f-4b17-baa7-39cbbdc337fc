<template>
  <div class="camera-controls">
    <h4>📷 自由相机控制</h4>
    
    <!-- Camera Mode Toggle -->
    <div class="control-group">
      <label class="toggle-label">
        <input 
          type="checkbox" 
          v-model="isCameraActive" 
          @change="toggleCameraMode"
          class="toggle-checkbox"
        />
        <span class="toggle-text">启用自由相机</span>
      </label>
    </div>
    
    <!-- Camera Configuration -->
    <div v-if="isCameraActive" class="config-section">
      
      <!-- Movement Speed -->
      <div class="control-group">
        <label>移动速度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="cameraConfig.movementSpeed"
            min="1"
            max="20"
            step="0.5"
            @input="updateConfig"
            class="slider"
          />
          <span class="value">{{ cameraConfig.movementSpeed }}</span>
        </div>
      </div>
      
      <!-- Rotation Speed -->
      <div class="control-group">
        <label>旋转速度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="cameraConfig.rotationSpeed"
            min="0.005"
            max="0.05"
            step="0.005"
            @input="updateConfig"
            class="slider"
          />
          <span class="value">{{ (cameraConfig.rotationSpeed * 1000).toFixed(0) }}</span>
        </div>
      </div>
      
      <!-- Horizontal Angle Range -->
      <div class="control-group">
        <label>水平角度范围:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="horizontalAngleDegrees"
            min="90"
            max="360"
            step="15"
            @input="updateHorizontalRange"
            class="slider"
          />
          <span class="value">{{ horizontalAngleDegrees }}°</span>
        </div>
      </div>
      
      <!-- Pitch Angle Range -->
      <div class="control-group">
        <label>俯仰角度范围:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="pitchAngleDegrees"
            min="30"
            max="180"
            step="15"
            @input="updatePitchRange"
            class="slider"
          />
          <span class="value">{{ pitchAngleDegrees }}°</span>
        </div>
      </div>
    </div>
    
    <!-- Mouse Control Settings -->
    <div v-if="isCameraActive" class="config-section">
      <div class="control-group">
        <label class="toggle-label">
          <input
            type="checkbox"
            v-model="cameraConfig.enableMouseControl"
            @change="updateConfig"
            class="toggle-checkbox"
          />
          <span class="toggle-text">启用鼠标控制</span>
        </label>
      </div>

      <div v-if="cameraConfig.enableMouseControl" class="control-group">
        <label>鼠标灵敏度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="cameraConfig.mouseSensitivity"
            min="0.0005"
            max="0.005"
            step="0.0001"
            @input="updateConfig"
            class="slider"
          />
          <span class="value">{{ (cameraConfig.mouseSensitivity * 1000).toFixed(1) }}</span>
        </div>
      </div>

      <div class="control-group">
        <label class="toggle-label">
          <input
            type="checkbox"
            v-model="cameraConfig.smoothMovement"
            @change="updateConfig"
            class="toggle-checkbox"
          />
          <span class="toggle-text">平滑移动</span>
        </label>
      </div>
    </div>

    <!-- Camera Visibility Controls -->
    <div v-if="isCameraActive" class="visibility-section">
      <h5>相机显示控制:</h5>
      <div class="control-group">
        <label class="toggle-label">
          <input
            type="checkbox"
            v-model="showCameraSphere"
            @change="toggleCameraSphere"
            class="toggle-checkbox"
          />
          <span class="toggle-text">显示相机球体</span>
        </label>
      </div>

      <div class="control-group">
        <label class="toggle-label">
          <input
            type="checkbox"
            v-model="showDirectionIndicator"
            @change="toggleDirectionIndicator"
            class="toggle-checkbox"
          />
          <span class="toggle-text">显示方向指示线</span>
        </label>
      </div>
    </div>

    <!-- Field of View Controls -->
    <div v-if="isCameraActive" class="fov-section">
      <h5>视野角度控制:</h5>

      <!-- Horizontal FOV -->
      <div class="control-group">
        <label>水平视角 (H-FOV):</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="fovSettings.horizontalFOV"
            min="10"
            max="120"
            step="1"
            @input="updateHorizontalFOV"
            class="slider horizontal-fov"
          />
          <span class="value">{{ fovSettings.horizontalFOV }}°</span>
        </div>
      </div>

      <!-- Vertical FOV -->
      <div class="control-group">
        <label>垂直视角 (V-FOV):</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="fovSettings.verticalFOV"
            min="5"
            max="90"
            step="1"
            @input="updateVerticalFOV"
            class="slider vertical-fov"
          />
          <span class="value">{{ fovSettings.verticalFOV }}°</span>
        </div>
      </div>

      <!-- FOV Information Display -->
      <div class="fov-info-section">
        <div class="control-group">
          <label>设定长宽比:</label>
          <span class="aspect-ratio-display">{{ calculatedAspectRatio }}</span>
        </div>

        <div class="control-group">
          <label>屏幕长宽比:</label>
          <span class="screen-aspect-display">{{ screenAspectRatio }}</span>
        </div>

        <div class="control-group">
          <label>视野匹配度:</label>
          <span class="fov-match-display" :class="fovMatchStatus.class">{{ fovMatchStatus.text }}</span>
        </div>

        <div class="control-group">
          <label>等效焦距:</label>
          <span class="focal-length-display">{{ equivalentFocalLength }}mm (35mm等效)</span>
        </div>

        <!-- FOV Precision Control -->
        <div class="fov-precision-section">
          <div class="control-group">
            <label class="toggle-label">
              <input
                type="checkbox"
                v-model="fovPrecisionMode"
                @change="updateFOVPrecisionMode"
                class="toggle-checkbox"
              />
              <span class="toggle-text">精确FOV模式</span>
            </label>
          </div>
          <div v-if="fovPrecisionMode" class="precision-info">
            <p class="precision-description">
              启用后，视野范围将严格按照设定的水平和垂直视场角显示，确保无畸变。
              禁用时，视野会适配屏幕比例，可能产生轻微畸变。
            </p>
          </div>
        </div>

        <!-- Viewport Frame Control -->
        <div class="viewport-frame-section">
          <div class="control-group">
            <label class="toggle-label">
              <input
                type="checkbox"
                v-model="viewportFrameEnabled"
                @change="updateViewportFrame"
                class="toggle-checkbox"
              />
              <span class="toggle-text">启用视野蒙板系统</span>
            </label>
          </div>
          <div v-if="viewportFrameEnabled" class="frame-config">
            <!-- Masking Mode Selection -->
            <div class="control-group">
              <label class="toggle-label">
                <input
                  type="checkbox"
                  v-model="viewportFrameConfig.useScreenSpaceMask"
                  @change="updateViewportFrameConfig"
                  class="toggle-checkbox"
                />
                <span class="toggle-text">屏幕空间蒙板 (推荐)</span>
              </label>
            </div>

            <!-- Aspect Ratio Control -->
            <div class="control-group">
              <label class="toggle-label">
                <input
                  type="checkbox"
                  v-model="viewportFrameConfig.maintainAspectRatio"
                  @change="updateViewportFrameConfig"
                  class="toggle-checkbox"
                />
                <span class="toggle-text">保持视野比例</span>
              </label>
            </div>

            <!-- Scale to Fit Control -->
            <div class="control-group">
              <label class="toggle-label">
                <input
                  type="checkbox"
                  v-model="viewportFrameConfig.scaleToFit"
                  @change="updateViewportFrameConfig"
                  class="toggle-checkbox"
                />
                <span class="toggle-text">缩放适配屏幕</span>
              </label>
            </div>

            <div class="control-group">
              <label>蒙板透明度:</label>
              <div class="slider-container">
                <input
                  type="range"
                  v-model="viewportFrameConfig.maskOpacity"
                  min="0"
                  max="1"
                  step="0.1"
                  @input="updateViewportFrameConfig"
                  class="slider"
                />
                <span class="value">{{ (viewportFrameConfig.maskOpacity * 100).toFixed(0) }}%</span>
              </div>
            </div>
            <div class="control-group">
              <label class="toggle-label">
                <input
                  type="checkbox"
                  v-model="viewportFrameConfig.showFrame"
                  @change="updateViewportFrameConfig"
                  class="toggle-checkbox"
                />
                <span class="toggle-text">显示框架边界</span>
              </label>
            </div>

            <!-- 3D Frame Distance (only for 3D mode) -->
            <div v-if="!viewportFrameConfig.useScreenSpaceMask" class="control-group">
              <label>框架距离:</label>
              <div class="slider-container">
                <input
                  type="range"
                  v-model="viewportFrameConfig.distance"
                  min="2"
                  max="20"
                  step="0.5"
                  @input="updateViewportFrameConfig"
                  class="slider"
                />
                <span class="value">{{ viewportFrameConfig.distance }}</span>
              </div>
            </div>
            <div class="frame-info">
              <p class="frame-description">
                🎯 <strong>视野蒙板系统</strong>：通过透明遮罩创建精确的矩形视野窗口。<br/>
                📐 <strong>保持比例</strong>：确保显示区域严格按照设定的水平/垂直FOV比例。<br/>
                🔍 <strong>缩放适配</strong>：自动调整相机视野以填充整个屏幕，无畸变显示。<br/>
                ⚡ <strong>屏幕空间蒙板</strong>：使用高性能CSS遮罩，比3D几何体更精确流畅。
              </p>

              <!-- Viewport Status Display -->
              <div v-if="viewportFrameEnabled" class="viewport-status">
                <h6>视野状态:</h6>
                <div class="status-grid">
                  <div class="status-item">
                    <span class="status-label">视野尺寸:</span>
                    <span class="status-value">{{ viewportDimensions.width.toFixed(0) }} × {{ viewportDimensions.height.toFixed(0) }}</span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">屏幕尺寸:</span>
                    <span class="status-value">{{ screenDimensions.width }} × {{ screenDimensions.height }}</span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">视野比例:</span>
                    <span class="status-value">{{ viewportAspectRatio }}</span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">缩放倍数:</span>
                    <span class="status-value">{{ viewportScaleFactor }}×</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Aspect Ratio Adaptation -->
        <div class="aspect-adaptation-section">
          <h6>长宽比适配:</h6>
          <div class="control-group">
            <label class="toggle-label">
              <input
                type="checkbox"
                v-model="aspectAdaptation.enabled"
                @change="updateAspectAdaptation"
                class="toggle-checkbox"
              />
              <span class="toggle-text">启用屏幕适配</span>
            </label>
          </div>

          <div v-if="aspectAdaptation.enabled" class="adaptation-options">
            <div class="control-group">
              <label>适配模式:</label>
              <select
                v-model="aspectAdaptation.mode"
                @change="updateAspectAdaptation"
                class="select-input"
              >
                <option value="fit">适应屏幕 (Fit)</option>
                <option value="fill">填充屏幕 (Fill)</option>
                <option value="stretch">拉伸适配 (Stretch)</option>
                <option value="crop">裁剪适配 (Crop)</option>
              </select>
            </div>

            <div class="control-group">
              <label>优先轴:</label>
              <select
                v-model="aspectAdaptation.priority"
                @change="updateAspectAdaptation"
                class="select-input"
              >
                <option value="horizontal">水平优先</option>
                <option value="vertical">垂直优先</option>
                <option value="auto">自动选择</option>
              </select>
            </div>

            <div class="adaptation-info">
              <div class="info-item">
                <span class="info-label">实际H-FOV:</span>
                <span class="info-value">{{ actualHorizontalFOV.toFixed(1) }}°</span>
              </div>
              <div class="info-item">
                <span class="info-label">实际V-FOV:</span>
                <span class="info-value">{{ actualVerticalFOV.toFixed(1) }}°</span>
              </div>
              <div class="info-item">
                <span class="info-label">畸变程度:</span>
                <span class="info-value" :class="distortionLevel.class">{{ distortionLevel.text }}</span>
              </div>
            </div>
          </div>
        </div>

        <button @click="resetFOVToDefault" class="reset-fov-button">
          🔄 重置FOV到默认值
        </button>
      </div>

      <!-- Target Size Input -->
      <div class="target-size-section">
        <h6>目标尺寸 (用于距离计算):</h6>
        <div class="input-grid-small">
          <div class="input-group">
            <label>宽度 (cm):</label>
            <input
              type="number"
              v-model.number="targetSize.width"
              step="0.1"
              min="0.1"
              @input="updateDistanceCalculation"
              class="number-input-small"
            />
          </div>
          <div class="input-group">
            <label>高度 (cm):</label>
            <input
              type="number"
              v-model.number="targetSize.height"
              step="0.1"
              min="0.1"
              @input="updateDistanceCalculation"
              class="number-input-small"
            />
          </div>
        </div>

        <!-- Distance Calculation Results -->
        <div class="distance-results">
          <div class="distance-info">
            <span class="distance-label">建议距离:</span>
            <span class="distance-value">{{ calculatedDistance.toFixed(2) }} cm</span>
          </div>
          <div class="distance-info">
            <span class="distance-label">基于宽度:</span>
            <span class="distance-value">{{ distanceByWidth.toFixed(2) }} cm</span>
          </div>
          <div class="distance-info">
            <span class="distance-label">基于高度:</span>
            <span class="distance-value">{{ distanceByHeight.toFixed(2) }} cm</span>
          </div>
        </div>
      </div>
    </div>



    <!-- Status Indicator -->
    <div v-if="isCameraActive" class="status-indicator">
      <div class="status-dot active"></div>
      <span>自由相机已激活</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { CameraConfig, CameraPreset } from '../types/CameraTypes'

// Props
interface Props {
  onToggleCamera?: (active: boolean) => void
  onConfigUpdate?: (config: Partial<CameraConfig>) => void
  onJumpToCamera?: () => void
  onResetCamera?: () => void
  onToggleVisibility?: () => void
  onSetPosition?: (x: number, y: number, z: number, smooth: boolean) => void
  onSetRotation?: (x: number, y: number, z: number, smooth: boolean) => void
  onLookAt?: (x: number, y: number, z: number, smooth: boolean) => void
  onApplyPreset?: (preset: CameraPreset, smooth: boolean) => void
  onToggleCameraSphere?: (visible: boolean) => void
  onToggleDirectionIndicator?: (visible: boolean) => void
  onUpdateFOV?: (horizontalFOV: number, verticalFOV: number, aspectRatio: number) => void
  onSetPrecisionMode?: (enabled: boolean, forceAspectRatio: boolean) => void
  onSetViewportFrame?: (enabled: boolean) => void
  onUpdateViewportFrameConfig?: (config: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  onToggleCamera: () => {},
  onConfigUpdate: () => {},
  onJumpToCamera: () => {},
  onResetCamera: () => {},
  onToggleVisibility: () => {}
})

// Reactive state
const isCameraActive = ref(false)
const isCameraVisible = ref(true)
const showCameraSphere = ref(true)
const showDirectionIndicator = ref(true)

// FOV Settings
const fovSettings = reactive({
  horizontalFOV: 60, // 默认水平视角60度
  verticalFOV: 30    // 默认垂直视角30度
})

// Target Size for Distance Calculation
const targetSize = reactive({
  width: 20,  // 默认目标宽度20cm
  height: 10  // 默认目标高度10cm
})

// Aspect Ratio Adaptation
const aspectAdaptation = reactive({
  enabled: false,
  mode: 'fit', // fit, fill, stretch, crop
  priority: 'auto' // horizontal, vertical, auto
})

// FOV Precision Mode
const fovPrecisionMode = ref(true) // 默认启用精确FOV模式

// Viewport Frame Control
const viewportFrameEnabled = ref(false) // 默认关闭视野框架
const viewportFrameConfig = reactive({
  distance: 5,
  maskOpacity: 0.3,
  showFrame: true,
  frameColor: 0xffffff,
  frameOpacity: 0.8,
  maskColor: 0x000000,
  // Enhanced masking properties
  useScreenSpaceMask: true,
  maintainAspectRatio: true,
  scaleToFit: true
})

// Screen dimensions
const screenDimensions = reactive({
  width: window.innerWidth,
  height: window.innerHeight
})

// Viewport dimensions (computed from viewport frame status)
const viewportDimensions = reactive({
  width: 0,
  height: 0
})

const cameraConfig = reactive<CameraConfig>({
  fov: 30, // 使用V-FOV作为基础FOV，将由H-FOV和V-FOV控制
  horizontalAngleRange: Math.PI * 2,
  pitchAngleRange: Math.PI * 0.8,
  movementSpeed: 5,
  rotationSpeed: 0.02,
  near: 1, // Match main camera default
  far: 1000,
  enableMouseControl: false, // 默认关闭鼠标控制
  mouseSensitivity: 0.002,
  smoothMovement: false,
  smoothRotation: false,
  smoothFactor: 0.1,
  // FOV现在由H-FOV和V-FOV控制
  horizontalFOV: 60,
  verticalFOV: 30,
  aspectRatio: 2.0
})

const cameraPosition = reactive({
  x: 10,
  y: 10,
  z: 10
})

const cameraRotation = reactive({
  x: 0,
  y: 0,
  z: 0
})

// 输入控制
const inputPosition = reactive({
  x: 10,
  y: 10,
  z: 10
})

const inputRotation = reactive({
  pitch: 0, // 俯仰角度（度）
  yaw: 0    // 偏航角度（度）
})

const lookAtTarget = reactive({
  x: 0,
  y: 0,
  z: 0
})

// 预设位置
const cameraPresets: CameraPreset[] = [
  {
    name: '🏠 默认',
    position: { x: 10, y: 10, z: 10 },
    rotation: { x: 0, y: 0, z: 0 },
    description: '默认相机位置'
  },
  {
    name: '🚗 车辆视角',
    position: { x: 0, y: 5, z: 15 },
    rotation: { x: -0.2, y: 0, z: 0 },
    description: '观察车辆的最佳角度'
  },
  {
    name: '🌅 侧面视角',
    position: { x: 20, y: 8, z: 0 },
    rotation: { x: 0, y: -Math.PI / 2, z: 0 },
    description: '从侧面观察'
  },
  {
    name: '🔝 俯视',
    position: { x: 0, y: 30, z: 0 },
    rotation: { x: -Math.PI / 2, y: 0, z: 0 },
    description: '从上方俯视'
  },
  {
    name: '👁️ 近距离',
    position: { x: 5, y: 3, z: 8 },
    rotation: { x: -0.1, y: 0.3, z: 0 },
    description: '近距离观察细节'
  }
]

// Computed properties for angle conversions
const horizontalAngleDegrees = computed({
  get: () => Math.round(cameraConfig.horizontalAngleRange * 180 / Math.PI),
  set: (value: number) => {
    cameraConfig.horizontalAngleRange = value * Math.PI / 180
  }
})

const pitchAngleDegrees = computed({
  get: () => Math.round(cameraConfig.pitchAngleRange * 180 / Math.PI),
  set: (value: number) => {
    cameraConfig.pitchAngleRange = value * Math.PI / 180
  }
})

// Computed properties for FOV calculations
const calculatedAspectRatio = computed(() => {
  const horizontalRad = fovSettings.horizontalFOV * Math.PI / 180
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180

  // 计算长宽比：tan(水平角/2) / tan(垂直角/2)
  const aspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)
  return `${aspectRatio.toFixed(2)}:1`
})

const calculatedDistance = computed(() => {
  // 基于垂直视角和目标高度计算距离
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180
  return (targetSize.height / 2) / Math.tan(verticalRad / 2)
})

const distanceByWidth = computed(() => {
  // 基于水平视角和目标宽度计算距离
  const horizontalRad = fovSettings.horizontalFOV * Math.PI / 180
  return (targetSize.width / 2) / Math.tan(horizontalRad / 2)
})

const distanceByHeight = computed(() => {
  // 基于垂直视角和目标高度计算距离
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180
  return (targetSize.height / 2) / Math.tan(verticalRad / 2)
})

const equivalentFocalLength = computed(() => {
  // 计算35mm等效焦距（基于垂直FOV）
  // 35mm胶片的垂直尺寸是24mm
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180
  const focalLength = (24 / 2) / Math.tan(verticalRad / 2)
  return Math.round(focalLength)
})

// Screen aspect ratio
const screenAspectRatio = computed(() => {
  const ratio = screenDimensions.width / screenDimensions.height
  return `${ratio.toFixed(2)}:1`
})

// Calculate actual FOV based on screen adaptation
const actualHorizontalFOV = computed(() => {
  if (!aspectAdaptation.enabled) {
    return fovSettings.horizontalFOV
  }

  const screenAspect = screenDimensions.width / screenDimensions.height
  const fovAspect = Math.tan(fovSettings.horizontalFOV * Math.PI / 360) / Math.tan(fovSettings.verticalFOV * Math.PI / 360)

  switch (aspectAdaptation.mode) {
    case 'fit':
      if (screenAspect > fovAspect) {
        // Screen is wider, adjust horizontal FOV
        return 2 * Math.atan(Math.tan(fovSettings.verticalFOV * Math.PI / 360) * screenAspect) * 180 / Math.PI
      }
      return fovSettings.horizontalFOV

    case 'fill':
      if (screenAspect < fovAspect) {
        // Screen is narrower, expand horizontal FOV
        return 2 * Math.atan(Math.tan(fovSettings.verticalFOV * Math.PI / 360) * screenAspect) * 180 / Math.PI
      }
      return fovSettings.horizontalFOV

    case 'stretch':
      return 2 * Math.atan(Math.tan(fovSettings.verticalFOV * Math.PI / 360) * screenAspect) * 180 / Math.PI

    case 'crop':
      return fovSettings.horizontalFOV

    default:
      return fovSettings.horizontalFOV
  }
})

const actualVerticalFOV = computed(() => {
  if (!aspectAdaptation.enabled) {
    return fovSettings.verticalFOV
  }

  const screenAspect = screenDimensions.width / screenDimensions.height
  const fovAspect = Math.tan(fovSettings.horizontalFOV * Math.PI / 360) / Math.tan(fovSettings.verticalFOV * Math.PI / 360)

  switch (aspectAdaptation.mode) {
    case 'fit':
      if (screenAspect < fovAspect) {
        // Screen is taller, adjust vertical FOV
        return 2 * Math.atan(Math.tan(fovSettings.horizontalFOV * Math.PI / 360) / screenAspect) * 180 / Math.PI
      }
      return fovSettings.verticalFOV

    case 'fill':
      if (screenAspect > fovAspect) {
        // Screen is wider, expand vertical FOV
        return 2 * Math.atan(Math.tan(fovSettings.horizontalFOV * Math.PI / 360) / screenAspect) * 180 / Math.PI
      }
      return fovSettings.verticalFOV

    case 'stretch':
      return 2 * Math.atan(Math.tan(fovSettings.horizontalFOV * Math.PI / 360) / screenAspect) * 180 / Math.PI

    case 'crop':
      return fovSettings.verticalFOV

    default:
      return fovSettings.verticalFOV
  }
})

// Calculate FOV match status
const fovMatchStatus = computed(() => {
  const screenAspect = screenDimensions.width / screenDimensions.height
  const fovAspect = Math.tan(fovSettings.horizontalFOV * Math.PI / 360) / Math.tan(fovSettings.verticalFOV * Math.PI / 360)

  if (fovPrecisionMode.value) {
    // 精确FOV模式下，检查是否完全匹配
    const aspectDifference = Math.abs(screenAspect - fovAspect) / fovAspect

    if (aspectDifference < 0.01) {
      return { text: '完美匹配', class: 'fov-perfect' }
    } else if (aspectDifference < 0.05) {
      return { text: '精确匹配', class: 'fov-precise' }
    } else {
      return { text: '强制精确', class: 'fov-forced' }
    }
  } else {
    // 屏幕适配模式下，显示畸变程度
    const distortion = Math.abs(screenAspect - fovAspect) / fovAspect

    if (distortion < 0.05) {
      return { text: '无畸变', class: 'distortion-none' }
    } else if (distortion < 0.15) {
      return { text: '轻微畸变', class: 'distortion-low' }
    } else if (distortion < 0.3) {
      return { text: '中等畸变', class: 'distortion-medium' }
    } else {
      return { text: '严重畸变', class: 'distortion-high' }
    }
  }
})

// Calculate distortion level (保留原有功能)
const distortionLevel = computed(() => {
  const screenAspect = screenDimensions.width / screenDimensions.height
  const fovAspect = Math.tan(fovSettings.horizontalFOV * Math.PI / 360) / Math.tan(fovSettings.verticalFOV * Math.PI / 360)
  const distortion = Math.abs(screenAspect - fovAspect) / fovAspect

  if (distortion < 0.05) {
    return { text: '无畸变', class: 'distortion-none' }
  } else if (distortion < 0.15) {
    return { text: '轻微畸变', class: 'distortion-low' }
  } else if (distortion < 0.3) {
    return { text: '中等畸变', class: 'distortion-medium' }
  } else {
    return { text: '严重畸变', class: 'distortion-high' }
  }
})

// Viewport status computed properties
const viewportAspectRatio = computed(() => {
  if (viewportDimensions.width === 0 || viewportDimensions.height === 0) {
    return '0:0'
  }
  const ratio = viewportDimensions.width / viewportDimensions.height
  return `${ratio.toFixed(2)}:1`
})

const viewportScaleFactor = computed(() => {
  if (viewportDimensions.width === 0 || viewportDimensions.height === 0) {
    return '1.00'
  }
  const scaleX = screenDimensions.width / viewportDimensions.width
  const scaleY = screenDimensions.height / viewportDimensions.height
  const scale = Math.min(scaleX, scaleY)
  return scale.toFixed(2)
})

// Methods
const toggleCameraMode = () => {
  props.onToggleCamera?.(isCameraActive.value)
}

const updateConfig = () => {
  props.onConfigUpdate?.(cameraConfig)
}

const updateHorizontalRange = () => {
  updateConfig()
}

const updatePitchRange = () => {
  updateConfig()
}

const jumpToCamera = () => {
  props.onJumpToCamera?.()
}

const resetCameraPosition = () => {
  props.onResetCamera?.()
}

const toggleCameraVisibility = () => {
  isCameraVisible.value = !isCameraVisible.value
  props.onToggleVisibility?.()
}

// 新增方法
const applyPositionInput = () => {
  props.onSetPosition?.(inputPosition.x, inputPosition.y, inputPosition.z, cameraConfig.smoothMovement)
}

const applyRotationInput = () => {
  const pitchRad = inputRotation.pitch * Math.PI / 180
  const yawRad = inputRotation.yaw * Math.PI / 180
  props.onSetRotation?.(pitchRad, yawRad, 0, cameraConfig.smoothRotation)
}

const applyLookAt = () => {
  props.onLookAt?.(lookAtTarget.x, lookAtTarget.y, lookAtTarget.z, cameraConfig.smoothRotation)
}

const applyPreset = (preset: CameraPreset) => {
  props.onApplyPreset?.(preset, cameraConfig.smoothMovement)
}

// 新增的显示控制方法
const toggleCameraSphere = () => {
  props.onToggleCameraSphere?.(showCameraSphere.value)
}

const toggleDirectionIndicator = () => {
  props.onToggleDirectionIndicator?.(showDirectionIndicator.value)
}

// FOV控制方法 - 改进版本，支持精确FOV模式
const updateFOVSettings = () => {
  let finalHFOV: number
  let finalVFOV: number
  let finalAspectRatio: number

  if (fovPrecisionMode.value) {
    // 精确FOV模式：使用用户设定的精确值
    finalHFOV = fovSettings.horizontalFOV
    finalVFOV = fovSettings.verticalFOV

    // 计算精确的长宽比
    const horizontalRad = finalHFOV * Math.PI / 180
    const verticalRad = finalVFOV * Math.PI / 180
    finalAspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)
  } else {
    // 屏幕适配模式：使用计算后的实际值
    finalHFOV = actualHorizontalFOV.value
    finalVFOV = actualVerticalFOV.value

    const horizontalRad = finalHFOV * Math.PI / 180
    const verticalRad = finalVFOV * Math.PI / 180
    finalAspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)
  }

  // 同步更新cameraConfig
  cameraConfig.fov = finalVFOV
  cameraConfig.horizontalFOV = finalHFOV
  cameraConfig.verticalFOV = finalVFOV
  cameraConfig.aspectRatio = finalAspectRatio

  // 通知父组件更新FOV和配置
  props.onUpdateFOV?.(finalHFOV, finalVFOV, finalAspectRatio)
  props.onConfigUpdate?.(cameraConfig)

  // 在精确模式下，输出调试信息
  if (fovPrecisionMode.value && process.env.NODE_ENV === 'development') {
    console.log('Precise FOV Mode:', {
      horizontalFOV: finalHFOV,
      verticalFOV: finalVFOV,
      aspectRatio: finalAspectRatio.toFixed(3),
      screenAspect: (screenDimensions.width / screenDimensions.height).toFixed(3),
      willForceAspect: Math.abs(finalAspectRatio - screenDimensions.width / screenDimensions.height) > 0.01
    })
  }
}

// 单独更新水平FOV
const updateHorizontalFOV = () => {
  updateFOVSettings()
  updateViewportDimensions()
}

// 单独更新垂直FOV
const updateVerticalFOV = () => {
  updateFOVSettings()
  updateViewportDimensions()
}

const updateDistanceCalculation = () => {
  // 距离计算会通过computed属性自动更新
  // 这里可以添加额外的逻辑，比如通知父组件
}

// 重置FOV到默认值
const resetFOVToDefault = () => {
  fovSettings.horizontalFOV = 60
  fovSettings.verticalFOV = 30
  aspectAdaptation.enabled = false
  aspectAdaptation.mode = 'fit'
  aspectAdaptation.priority = 'auto'
  updateFOVSettings()
}

// 屏幕适配方法
const updateAspectAdaptation = () => {
  updateFOVSettings()
}

// 精确FOV模式控制
const updateFOVPrecisionMode = () => {
  // 当切换精确FOV模式时，需要重新计算和应用FOV设置
  if (fovPrecisionMode.value) {
    // 启用精确模式：禁用屏幕适配，使用精确的FOV计算
    aspectAdaptation.enabled = false
  }

  // 通知父组件更新精确模式设置
  props.onSetPrecisionMode?.(fovPrecisionMode.value, true)

  // 重新应用FOV设置
  updateFOVSettings()
}

// 视野框架控制
const updateViewportFrame = () => {
  props.onSetViewportFrame?.(viewportFrameEnabled.value)
  updateViewportDimensions()
}

const updateViewportFrameConfig = () => {
  const config = {
    distance: viewportFrameConfig.distance,
    maskOpacity: viewportFrameConfig.maskOpacity,
    showFrame: viewportFrameConfig.showFrame,
    frameColor: viewportFrameConfig.frameColor,
    frameOpacity: viewportFrameConfig.frameOpacity,
    maskColor: viewportFrameConfig.maskColor,
    useScreenSpaceMask: viewportFrameConfig.useScreenSpaceMask,
    maintainAspectRatio: viewportFrameConfig.maintainAspectRatio,
    scaleToFit: viewportFrameConfig.scaleToFit
  }
  props.onUpdateViewportFrameConfig?.(config)

  // Update viewport dimensions display
  updateViewportDimensions()
}

// Update viewport dimensions from the viewport frame status
const updateViewportDimensions = () => {
  // This would be called by the parent component or through a callback
  // For now, we'll calculate it based on FOV settings
  if (viewportFrameEnabled.value) {
    const horizontalRad = fovSettings.horizontalFOV * Math.PI / 180
    const verticalRad = fovSettings.verticalFOV * Math.PI / 180
    const fovAspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)
    const canvasAspectRatio = screenDimensions.width / screenDimensions.height

    if (viewportFrameConfig.maintainAspectRatio) {
      if (fovAspectRatio > canvasAspectRatio) {
        viewportDimensions.width = screenDimensions.width
        viewportDimensions.height = screenDimensions.width / fovAspectRatio
      } else {
        viewportDimensions.height = screenDimensions.height
        viewportDimensions.width = screenDimensions.height * fovAspectRatio
      }
    } else {
      viewportDimensions.width = screenDimensions.width
      viewportDimensions.height = screenDimensions.height
    }
  }
}

// 更新屏幕尺寸
const updateScreenDimensions = () => {
  screenDimensions.width = window.innerWidth
  screenDimensions.height = window.innerHeight
  if (aspectAdaptation.enabled) {
    updateFOVSettings()
  }
}

// 窗口大小变化监听器
const handleResize = () => {
  updateScreenDimensions()
}

// Expose methods for parent component
const updatePosition = (x: number, y: number, z: number) => {
  cameraPosition.x = x
  cameraPosition.y = y
  cameraPosition.z = z
  // 同步到输入框
  inputPosition.x = x
  inputPosition.y = y
  inputPosition.z = z
}

const updateRotation = (x: number, y: number, z: number) => {
  cameraRotation.x = x
  cameraRotation.y = y
  cameraRotation.z = z
  // 同步到输入框（转换为度）
  inputRotation.pitch = x * 180 / Math.PI
  inputRotation.yaw = y * 180 / Math.PI
}

const setConfig = (config: CameraConfig) => {
  Object.assign(cameraConfig, config)
}

// 生命周期钩子
onMounted(() => {
  window.addEventListener('resize', handleResize)
  updateScreenDimensions()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  updatePosition,
  updateRotation,
  setConfig,
  isCameraActive,
  isCameraVisible,
  updateScreenDimensions
})
</script>

<style scoped>
.camera-controls {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #444;
}

.camera-controls h4 {
  margin-bottom: 15px;
  color: #FF6B35;
  font-size: 14px;
}

.camera-controls h5 {
  margin-bottom: 8px;
  color: #FFA500;
  font-size: 12px;
}

.control-group {
  margin-bottom: 12px;
}

.control-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 11px;
  color: #ccc;
  font-weight: bold;
}

.toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-checkbox {
  margin-right: 8px;
}

.toggle-text {
  font-size: 12px;
  color: #FF6B35;
  font-weight: bold;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.slider {
  flex: 1;
  max-width: 120px;
  height: 4px;
  background: #333;
  border-radius: 2px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: #FF6B35;
  border-radius: 50%;
  cursor: pointer;
}

.value {
  font-size: 10px;
  color: #FF6B35;
  font-weight: bold;
  min-width: 35px;
  text-align: right;
}

.config-section {
  background: rgba(255, 107, 53, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.position-display {
  background: rgba(255, 165, 0, 0.1);
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.position-info, .rotation-info {
  display: flex;
  gap: 12px;
  font-size: 10px;
  margin-bottom: 4px;
}

.position-info span, .rotation-info span {
  color: #FFA500;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.jump-button, .reset-button, .visibility-button {
  background: linear-gradient(45deg, #FF6B35, #F7931E);
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.jump-button:hover, .reset-button:hover, .visibility-button:hover {
  background: linear-gradient(45deg, #E55A2B, #E8851A);
  transform: translateY(-1px);
}

.controls-help {
  background: rgba(255, 107, 53, 0.05);
  padding: 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 107, 53, 0.2);
}

.help-text p {
  margin: 2px 0;
  font-size: 10px;
  color: #ccc;
}

.help-text strong {
  color: #FF6B35;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  padding: 6px 10px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 4px;
  font-size: 11px;
  color: #4CAF50;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4CAF50;
}

.status-dot.active {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 新增样式 */
.position-input-section {
  background: rgba(76, 175, 80, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.input-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 8px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.input-group label {
  font-size: 9px;
  color: #4CAF50;
  font-weight: bold;
}

.number-input {
  padding: 4px;
  border: 1px solid #4CAF50;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.3);
  color: #81C784;
  font-size: 10px;
  width: 100%;
}

.number-input:focus {
  outline: none;
  border-color: #66BB6A;
  background: rgba(0, 0, 0, 0.5);
}

.apply-button {
  padding: 6px 12px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  font-weight: bold;
  margin-bottom: 8px;
  transition: background-color 0.2s;
}

.apply-button:hover {
  background: #45a049;
}

.preset-section {
  background: rgba(76, 175, 80, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.preset-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.preset-button {
  padding: 6px 8px;
  background: rgba(76, 175, 80, 0.2);
  color: #81C784;
  border: 1px solid #4CAF50;
  border-radius: 4px;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  transition: all 0.2s;
}

.preset-button:hover {
  background: rgba(76, 175, 80, 0.4);
  color: #4CAF50;
  transform: translateY(-1px);
}

/* 显示控制区域样式 */
.visibility-section {
  background: rgba(76, 175, 80, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.visibility-section h5 {
  margin: 0 0 8px 0;
  color: #4CAF50;
  font-size: 10px;
  font-weight: bold;
}

.visibility-section .control-group {
  margin-bottom: 6px;
}

.visibility-section .toggle-text {
  color: #66BB6A;
  font-weight: 500;
}

/* FOV控制区域样式 */
.fov-section {
  background: rgba(63, 81, 181, 0.1);
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
  border: 1px solid rgba(63, 81, 181, 0.2);
}

.fov-section h5 {
  margin: 0 0 10px 0;
  color: #3F51B5;
  font-size: 10px;
  font-weight: bold;
}

.fov-section h6 {
  margin: 8px 0 4px 0;
  color: #5C6BC0;
  font-size: 9px;
  font-weight: bold;
}

.aspect-ratio-display {
  font-size: 11px;
  color: #3F51B5;
  font-weight: bold;
  background: rgba(63, 81, 181, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(63, 81, 181, 0.3);
}

.target-size-section {
  background: rgba(63, 81, 181, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
  border: 1px solid rgba(63, 81, 181, 0.1);
}

.input-grid-small {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
  margin-bottom: 8px;
}

.number-input-small {
  padding: 3px;
  border: 1px solid #3F51B5;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.3);
  color: #5C6BC0;
  font-size: 9px;
  width: 100%;
}

.number-input-small:focus {
  outline: none;
  border-color: #5C6BC0;
  background: rgba(0, 0, 0, 0.5);
}

.distance-results {
  background: rgba(63, 81, 181, 0.08);
  padding: 6px;
  border-radius: 3px;
  border: 1px solid rgba(63, 81, 181, 0.15);
}

.distance-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  font-size: 9px;
}

.distance-label {
  color: #5C6BC0;
  font-weight: 500;
}

.distance-value {
  color: #3F51B5;
  font-weight: bold;
  background: rgba(63, 81, 181, 0.1);
  padding: 1px 4px;
  border-radius: 2px;
}

/* FOV滑块特殊样式 */
.fov-section .slider::-webkit-slider-thumb {
  background: #3F51B5;
}

.fov-section .value {
  color: #3F51B5;
}

/* 水平和垂直FOV滑块区分样式 */
.horizontal-fov::-webkit-slider-thumb {
  background: #FF5722 !important;
}

.vertical-fov::-webkit-slider-thumb {
  background: #4CAF50 !important;
}

.fov-info-section {
  background: rgba(63, 81, 181, 0.08);
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0;
  border: 1px solid rgba(63, 81, 181, 0.15);
}

.focal-length-display {
  font-size: 10px;
  color: #3F51B5;
  font-weight: bold;
  background: rgba(63, 81, 181, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(63, 81, 181, 0.3);
}

.reset-fov-button {
  padding: 6px 12px;
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  margin-top: 6px;
  transition: background-color 0.2s;
  width: 100%;
}

.reset-fov-button:hover {
  background: #F57C00;
}

/* 屏幕适配样式 */
.screen-aspect-display {
  font-size: 10px;
  color: #9C27B0;
  font-weight: bold;
  background: rgba(156, 39, 176, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(156, 39, 176, 0.3);
}

.aspect-adaptation-section {
  background: rgba(156, 39, 176, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0;
  border: 1px solid rgba(156, 39, 176, 0.1);
}

.adaptation-options {
  margin-top: 8px;
  padding: 6px;
  background: rgba(156, 39, 176, 0.03);
  border-radius: 3px;
}

.select-input {
  padding: 3px;
  border: 1px solid #9C27B0;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.3);
  color: #BA68C8;
  font-size: 9px;
  width: 100%;
}

.select-input:focus {
  outline: none;
  border-color: #BA68C8;
  background: rgba(0, 0, 0, 0.5);
}

.adaptation-info {
  background: rgba(156, 39, 176, 0.08);
  padding: 6px;
  border-radius: 3px;
  margin-top: 6px;
  border: 1px solid rgba(156, 39, 176, 0.15);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  font-size: 9px;
}

.info-label {
  color: #BA68C8;
  font-weight: 500;
}

.info-value {
  color: #9C27B0;
  font-weight: bold;
  background: rgba(156, 39, 176, 0.1);
  padding: 1px 4px;
  border-radius: 2px;
}

/* FOV匹配状态颜色 */
.fov-match-display {
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid;
}

.fov-perfect {
  color: #4CAF50 !important;
  background: rgba(76, 175, 80, 0.1) !important;
  border-color: rgba(76, 175, 80, 0.3) !important;
}

.fov-precise {
  color: #2196F3 !important;
  background: rgba(33, 150, 243, 0.1) !important;
  border-color: rgba(33, 150, 243, 0.3) !important;
}

.fov-forced {
  color: #9C27B0 !important;
  background: rgba(156, 39, 176, 0.1) !important;
  border-color: rgba(156, 39, 176, 0.3) !important;
}

/* 畸变程度颜色 */
.distortion-none {
  color: #4CAF50 !important;
  background: rgba(76, 175, 80, 0.1) !important;
}

.distortion-low {
  color: #FF9800 !important;
  background: rgba(255, 152, 0, 0.1) !important;
}

.distortion-medium {
  color: #FF5722 !important;
  background: rgba(255, 87, 34, 0.1) !important;
}

.distortion-high {
  color: #F44336 !important;
  background: rgba(244, 67, 54, 0.1) !important;
}

/* 精确FOV模式样式 */
.fov-precision-section {
  background: rgba(156, 39, 176, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0;
  border: 1px solid rgba(156, 39, 176, 0.1);
}

.precision-description {
  font-size: 9px;
  color: #BA68C8;
  margin: 4px 0 0 0;
  line-height: 1.3;
  background: rgba(156, 39, 176, 0.03);
  padding: 4px 6px;
  border-radius: 3px;
  border-left: 2px solid rgba(156, 39, 176, 0.3);
}

/* 视野框架样式 */
.viewport-frame-section {
  background: rgba(255, 193, 7, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0;
  border: 1px solid rgba(255, 193, 7, 0.1);
}

.frame-config {
  margin-top: 8px;
  padding: 6px;
  background: rgba(255, 193, 7, 0.03);
  border-radius: 3px;
}

.frame-description {
  font-size: 9px;
  color: #FFC107;
  margin: 4px 0 0 0;
  line-height: 1.3;
  background: rgba(255, 193, 7, 0.03);
  padding: 4px 6px;
  border-radius: 3px;
  border-left: 2px solid rgba(255, 193, 7, 0.3);
}

.frame-info {
  background: rgba(255, 193, 7, 0.08);
  padding: 6px;
  border-radius: 3px;
  margin-top: 6px;
  border: 1px solid rgba(255, 193, 7, 0.15);
}

/* Viewport Status Styles */
.viewport-status {
  margin-top: 8px;
  padding: 6px;
  background: rgba(33, 150, 243, 0.05);
  border-radius: 3px;
  border: 1px solid rgba(33, 150, 243, 0.1);
}

.viewport-status h6 {
  margin: 0 0 4px 0;
  color: #2196F3;
  font-size: 9px;
  font-weight: bold;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 8px;
  padding: 2px 4px;
  background: rgba(33, 150, 243, 0.03);
  border-radius: 2px;
}

.status-label {
  color: #64B5F6;
  font-weight: 500;
}

.status-value {
  color: #2196F3;
  font-weight: bold;
  font-family: monospace;
}
</style>
