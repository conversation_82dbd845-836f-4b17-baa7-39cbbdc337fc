<template>
  <div class="fov-controls">
    <!-- FOV Settings -->
    <div class="fov-section">
      <h5>视野范围模拟:</h5>
      
      <!-- Horizontal FOV -->
      <div class="control-group">
        <label>水平视野 (H-FOV):</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="fovSettings.horizontalFOV"
            min="10"
            max="120"
            step="1"
            @input="updateHorizontalFOV"
            class="slider horizontal-fov"
          />
          <span class="value">{{ fovSettings.horizontalFOV }}°</span>
        </div>
      </div>
      
      <!-- Vertical FOV -->
      <div class="control-group">
        <label>垂直视野 (V-FOV):</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="fovSettings.verticalFOV"
            min="5"
            max="90"
            step="1"
            @input="updateVerticalFOV"
            class="slider vertical-fov"
          />
          <span class="value">{{ fovSettings.verticalFOV }}°</span>
        </div>
      </div>
      
      <!-- FOV Information Display -->
      <div class="fov-info-section">
        <div class="control-group">
          <label>模拟长宽比:</label>
          <span class="aspect-ratio-display">{{ calculatedAspectRatio }}</span>
        </div>
        
        <div class="control-group">
          <label>等效焦距:</label>
          <span class="focal-length-display">{{ equivalentFocalLength }}mm (35mm等效)</span>
        </div>
        
        <button @click="resetFOVToDefault" class="reset-fov-button">
          🔄 重置视野到默认值
        </button>
      </div>
      
      <!-- Target Size Input -->
      <div class="target-size-section">
        <h6>目标尺寸 (用于距离计算):</h6>
        <div class="input-grid-small">
          <div class="input-group">
            <label>宽度 (cm):</label>
            <input 
              type="number" 
              v-model.number="targetSize.width" 
              step="0.1"
              min="0.1"
              @input="updateDistanceCalculation"
              class="number-input-small"
            />
          </div>
          <div class="input-group">
            <label>高度 (cm):</label>
            <input 
              type="number" 
              v-model.number="targetSize.height" 
              step="0.1"
              min="0.1"
              @input="updateDistanceCalculation"
              class="number-input-small"
            />
          </div>
        </div>
        
        <!-- Distance Calculation Results -->
        <div class="distance-results">
          <div class="distance-info">
            <span class="distance-label">建议距离:</span>
            <span class="distance-value">{{ calculatedDistance.toFixed(2) }} cm</span>
          </div>
          <div class="distance-info">
            <span class="distance-label">基于宽度:</span>
            <span class="distance-value">{{ distanceByWidth.toFixed(2) }} cm</span>
          </div>
          <div class="distance-info">
            <span class="distance-label">基于高度:</span>
            <span class="distance-value">{{ distanceByHeight.toFixed(2) }} cm</span>
          </div>
        </div>
      </div>
    </div>

    <!-- FOV Presets -->
    <div class="fov-presets-section">
      <h5>视野预设:</h5>
      <div class="preset-buttons">
        <button 
          v-for="preset in fovPresets" 
          :key="preset.name"
          @click="applyFOVPreset(preset)"
          class="preset-button"
          :title="preset.description"
        >
          {{ preset.name }}
        </button>
      </div>
    </div>

    <!-- Manual FOV Input -->
    <div class="manual-fov-section">
      <h5>精确视野设置:</h5>
      <div class="input-grid-small">
        <div class="input-group">
          <label>H-FOV (°):</label>
          <input 
            type="number" 
            v-model.number="inputHFOV" 
            step="1"
            min="10"
            max="120"
            @keyup.enter="applyManualFOV"
            class="number-input-small"
          />
        </div>
        <div class="input-group">
          <label>V-FOV (°):</label>
          <input 
            type="number" 
            v-model.number="inputVFOV" 
            step="1"
            min="5"
            max="90"
            @keyup.enter="applyManualFOV"
            class="number-input-small"
          />
        </div>
      </div>
      <button @click="applyManualFOV" class="apply-button">
        🎯 应用视野设置
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

// Props
interface Props {
  onUpdateFOV?: (horizontalFOV: number, verticalFOV: number, aspectRatio: number) => void
}

const props = defineProps<Props>()

// FOV Settings - 现在用于视野模拟
const fovSettings = reactive({
  horizontalFOV: 60, // 默认水平视野60度
  verticalFOV: 30    // 默认垂直视野30度
})

// Target Size for Distance Calculation
const targetSize = reactive({
  width: 20,  // 默认目标宽度20cm
  height: 10  // 默认目标高度10cm
})

// Manual input
const inputHFOV = ref(60)
const inputVFOV = ref(30)

// FOV Presets - 更新描述以反映模拟功能
const fovPresets = [
  {
    name: '🎬 电影',
    horizontalFOV: 90,
    verticalFOV: 25,
    description: '电影宽屏视野模拟 (3.6:1)'
  },
  {
    name: '📺 标准',
    horizontalFOV: 60,
    verticalFOV: 30,
    description: '标准视野模拟 (2:1)'
  },
  {
    name: '👤 人像',
    horizontalFOV: 40,
    verticalFOV: 30,
    description: '人像摄影视野模拟 (1.33:1)'
  },
  {
    name: '🏢 建筑',
    horizontalFOV: 100,
    verticalFOV: 75,
    description: '建筑广角视野模拟 (1.33:1)'
  },
  {
    name: '📱 竖屏',
    horizontalFOV: 30,
    verticalFOV: 50,
    description: '手机竖屏视野模拟 (0.6:1)'
  },
  {
    name: '🌄 全景',
    horizontalFOV: 110,
    verticalFOV: 40,
    description: '全景摄影视野模拟 (2.75:1)'
  }
]

// Computed properties
const calculatedAspectRatio = computed(() => {
  const horizontalRad = fovSettings.horizontalFOV * Math.PI / 180
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180
  
  const aspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)
  return `${aspectRatio.toFixed(2)}:1`
})

const calculatedDistance = computed(() => {
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180
  return (targetSize.height / 2) / Math.tan(verticalRad / 2)
})

const distanceByWidth = computed(() => {
  const horizontalRad = fovSettings.horizontalFOV * Math.PI / 180
  return (targetSize.width / 2) / Math.tan(horizontalRad / 2)
})

const distanceByHeight = computed(() => {
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180
  return (targetSize.height / 2) / Math.tan(verticalRad / 2)
})

const equivalentFocalLength = computed(() => {
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180
  const focalLength = (24 / 2) / Math.tan(verticalRad / 2)
  return Math.round(focalLength)
})

// Methods - 现在用于视野模拟而不是直接修改相机FOV
const updateFOVSettings = () => {
  const horizontalRad = fovSettings.horizontalFOV * Math.PI / 180
  const verticalRad = fovSettings.verticalFOV * Math.PI / 180
  const aspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)
  
  // 同步到手动输入
  inputHFOV.value = fovSettings.horizontalFOV
  inputVFOV.value = fovSettings.verticalFOV
  
  // 通知父组件更新视野模拟设置
  props.onUpdateFOV?.(fovSettings.horizontalFOV, fovSettings.verticalFOV, aspectRatio)
}

const updateHorizontalFOV = () => {
  updateFOVSettings()
}

const updateVerticalFOV = () => {
  updateFOVSettings()
}

const updateDistanceCalculation = () => {
  // 距离计算会通过computed属性自动更新
}

const resetFOVToDefault = () => {
  fovSettings.horizontalFOV = 60
  fovSettings.verticalFOV = 30
  updateFOVSettings()
}

const applyFOVPreset = (preset: any) => {
  fovSettings.horizontalFOV = preset.horizontalFOV
  fovSettings.verticalFOV = preset.verticalFOV
  updateFOVSettings()
}

const applyManualFOV = () => {
  fovSettings.horizontalFOV = Math.max(10, Math.min(120, inputHFOV.value))
  fovSettings.verticalFOV = Math.max(5, Math.min(90, inputVFOV.value))
  updateFOVSettings()
}

// Expose methods for parent component
const setFOV = (horizontalFOV: number, verticalFOV: number) => {
  fovSettings.horizontalFOV = horizontalFOV
  fovSettings.verticalFOV = verticalFOV
  inputHFOV.value = horizontalFOV
  inputVFOV.value = verticalFOV
}

defineExpose({
  setFOV,
  resetFOVToDefault
})
</script>

<style scoped>
.fov-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* FOV控制区域样式 */
.fov-section {
  background: rgba(63, 81, 181, 0.1);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid rgba(63, 81, 181, 0.2);
}

.fov-section h5 {
  margin: 0 0 10px 0;
  color: #3F51B5;
  font-size: 12px;
  font-weight: bold;
}

.fov-section h6 {
  margin: 8px 0 4px 0;
  color: #5C6BC0;
  font-size: 10px;
  font-weight: bold;
}

.control-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 8px;
}

.control-group label {
  font-size: 10px;
  color: #3F51B5;
  font-weight: bold;
  min-width: 80px;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.slider {
  flex: 1;
  max-width: 120px;
  height: 4px;
  background: #333;
  border-radius: 2px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: #3F51B5;
  border-radius: 50%;
  cursor: pointer;
}

.horizontal-fov::-webkit-slider-thumb {
  background: #FF5722 !important;
}

.vertical-fov::-webkit-slider-thumb {
  background: #4CAF50 !important;
}

.value {
  font-size: 10px;
  color: #3F51B5;
  font-weight: bold;
  min-width: 35px;
  text-align: right;
}

.fov-info-section {
  background: rgba(63, 81, 181, 0.08);
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0;
  border: 1px solid rgba(63, 81, 181, 0.15);
}

.aspect-ratio-display, .focal-length-display {
  font-size: 10px;
  color: #3F51B5;
  font-weight: bold;
  background: rgba(63, 81, 181, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(63, 81, 181, 0.3);
}

.reset-fov-button {
  padding: 6px 12px;
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  margin-top: 6px;
  transition: background-color 0.2s;
  width: 100%;
}

.reset-fov-button:hover {
  background: #F57C00;
}

.target-size-section {
  background: rgba(63, 81, 181, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
  border: 1px solid rgba(63, 81, 181, 0.1);
}

.input-grid-small {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
  margin-bottom: 8px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.input-group label {
  font-size: 9px;
  color: #3F51B5;
  font-weight: bold;
  min-width: auto;
}

.number-input-small {
  padding: 3px;
  border: 1px solid #3F51B5;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.3);
  color: #5C6BC0;
  font-size: 9px;
  width: 100%;
}

.number-input-small:focus {
  outline: none;
  border-color: #5C6BC0;
  background: rgba(0, 0, 0, 0.5);
}

.distance-results {
  background: rgba(63, 81, 181, 0.08);
  padding: 6px;
  border-radius: 3px;
  border: 1px solid rgba(63, 81, 181, 0.15);
}

.distance-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  font-size: 9px;
}

.distance-label {
  color: #5C6BC0;
  font-weight: 500;
}

.distance-value {
  color: #3F51B5;
  font-weight: bold;
  background: rgba(63, 81, 181, 0.1);
  padding: 1px 4px;
  border-radius: 2px;
}

/* FOV预设区域 */
.fov-presets-section {
  background: rgba(156, 39, 176, 0.1);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid rgba(156, 39, 176, 0.2);
}

.fov-presets-section h5 {
  margin: 0 0 8px 0;
  color: #9C27B0;
  font-size: 12px;
  font-weight: bold;
}

.preset-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.preset-button {
  padding: 6px 8px;
  background: rgba(156, 39, 176, 0.2);
  color: #BA68C8;
  border: 1px solid #9C27B0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  transition: all 0.2s;
}

.preset-button:hover {
  background: rgba(156, 39, 176, 0.4);
  color: #9C27B0;
  transform: translateY(-1px);
}

/* 手动FOV设置区域 */
.manual-fov-section {
  background: rgba(255, 152, 0, 0.1);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid rgba(255, 152, 0, 0.2);
}

.manual-fov-section h5 {
  margin: 0 0 8px 0;
  color: #FF9800;
  font-size: 12px;
  font-weight: bold;
}

.apply-button {
  padding: 6px 12px;
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  font-weight: bold;
  margin-top: 8px;
  transition: background-color 0.2s;
  width: 100%;
}

.apply-button:hover {
  background: #F57C00;
}
</style>
