<template>
  <div class="global-camera-controls">
    <h4>🌍 全局视角控制</h4>
    
    <!-- Global Camera Mode Toggle -->
    <div class="control-group">
      <label class="toggle-label">
        <input 
          type="checkbox" 
          v-model="isGlobalCameraActive" 
          @change="toggleGlobalCameraMode"
          class="toggle-checkbox"
        />
        <span class="toggle-text">启用全局视角移动</span>
      </label>
    </div>
    
    <!-- Global Camera Configuration -->
    <div v-if="isGlobalCameraActive" class="config-section">
      <!-- Movement Speed -->
      <div class="control-group">
        <label>移动速度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="globalCameraConfig.movementSpeed"
            min="5"
            max="50"
            step="1"
            @input="updateGlobalConfig"
            class="slider"
          />
          <span class="value">{{ globalCameraConfig.movementSpeed }}</span>
        </div>
      </div>
      
      <!-- Smoothing -->
      <div class="control-group">
        <label>移动平滑度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="globalCameraConfig.smoothing"
            min="0.05"
            max="0.3"
            step="0.01"
            @input="updateGlobalConfig"
            class="slider"
          />
          <span class="value">{{ (globalCameraConfig.smoothing * 100).toFixed(0) }}%</span>
        </div>
      </div>
      
      <!-- Height Range -->
      <div class="control-group">
        <label>最小高度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="globalCameraConfig.minHeight"
            min="1"
            max="20"
            step="1"
            @input="updateGlobalConfig"
            class="slider"
          />
          <span class="value">{{ globalCameraConfig.minHeight }}</span>
        </div>
      </div>
      
      <div class="control-group">
        <label>最大高度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="globalCameraConfig.maxHeight"
            min="50"
            max="300"
            step="10"
            @input="updateGlobalConfig"
            class="slider"
          />
          <span class="value">{{ globalCameraConfig.maxHeight }}</span>
        </div>
      </div>
      
      <!-- Boundary Size -->
      <div class="control-group">
        <label>移动边界:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="globalCameraConfig.boundarySize"
            min="100"
            max="1000"
            step="50"
            @input="updateGlobalConfig"
            class="slider"
          />
          <span class="value">{{ globalCameraConfig.boundarySize }}</span>
        </div>
      </div>
    </div>
    
    <!-- Camera Position Display -->
    <div v-if="isGlobalCameraActive" class="position-display">
      <h5>相机位置:</h5>
      <div class="position-info">
        <span>X: {{ cameraPosition.x.toFixed(1) }}</span>
        <span>Y: {{ cameraPosition.y.toFixed(1) }}</span>
        <span>Z: {{ cameraPosition.z.toFixed(1) }}</span>
      </div>
    </div>
    
    <!-- Action Buttons -->
    <div v-if="isGlobalCameraActive" class="action-buttons">
      <button @click="resetToGlobalView" class="reset-button">
        🌍 重置全局视角
      </button>
      <button @click="setTopDownView" class="topdown-button">
        ⬇️ 俯视视角
      </button>
      <button @click="setIsometricView" class="iso-button">
        📐 等距视角
      </button>
    </div>
    
    <!-- Controls Help -->
    <div v-if="isGlobalCameraActive" class="controls-help">
      <h5>控制说明:</h5>
      <div class="help-text">
        <p><strong>WASD/方向键:</strong> 水平移动</p>
        <p><strong>Q/E:</strong> 上升/下降</p>
        <p><strong>注意:</strong> 保持全局俯视角度</p>
      </div>
    </div>
    
    <!-- Status Indicator -->
    <div v-if="isGlobalCameraActive" class="status-indicator">
      <div class="status-dot active"></div>
      <span>全局视角已激活</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { GlobalCameraConfig } from '../utils/GlobalCamera'

// Props
interface Props {
  onToggleGlobalCamera?: (active: boolean) => void
  onConfigUpdate?: (config: Partial<GlobalCameraConfig>) => void
  onResetToGlobalView?: () => void
  onSetTopDownView?: () => void
  onSetIsometricView?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  onToggleGlobalCamera: () => {},
  onConfigUpdate: () => {},
  onResetToGlobalView: () => {},
  onSetTopDownView: () => {},
  onSetIsometricView: () => {}
})

// Reactive state
const isGlobalCameraActive = ref(false)

const globalCameraConfig = reactive<GlobalCameraConfig>({
  movementSpeed: 20,
  smoothing: 0.1,
  minHeight: 5,
  maxHeight: 200,
  boundarySize: 500
})

const cameraPosition = reactive({
  x: 0,
  y: 50,
  z: 0
})

// Methods
const toggleGlobalCameraMode = () => {
  props.onToggleGlobalCamera?.(isGlobalCameraActive.value)
}

const updateGlobalConfig = () => {
  props.onConfigUpdate?.(globalCameraConfig)
}

const resetToGlobalView = () => {
  props.onResetToGlobalView?.()
}

const setTopDownView = () => {
  props.onSetTopDownView?.()
}

const setIsometricView = () => {
  props.onSetIsometricView?.()
}

// Expose methods for parent component
const updatePosition = (x: number, y: number, z: number) => {
  cameraPosition.x = x
  cameraPosition.y = y
  cameraPosition.z = z
}

const setConfig = (config: GlobalCameraConfig) => {
  Object.assign(globalCameraConfig, config)
}

defineExpose({
  updatePosition,
  setConfig,
  isGlobalCameraActive
})
</script>

<style scoped>
.global-camera-controls {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #444;
}

.global-camera-controls h4 {
  margin-bottom: 15px;
  color: #4CAF50;
  font-size: 14px;
}

.global-camera-controls h5 {
  margin-bottom: 8px;
  color: #81C784;
  font-size: 12px;
}

.control-group {
  margin-bottom: 12px;
}

.control-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 11px;
  color: #ccc;
  font-weight: bold;
}

.toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-checkbox {
  margin-right: 8px;
}

.toggle-text {
  font-size: 12px;
  color: #4CAF50;
  font-weight: bold;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.slider {
  flex: 1;
  max-width: 120px;
  height: 4px;
  background: #333;
  border-radius: 2px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: #4CAF50;
  border-radius: 50%;
  cursor: pointer;
}

.value {
  font-size: 10px;
  color: #4CAF50;
  font-weight: bold;
  min-width: 35px;
  text-align: right;
}

.config-section {
  background: rgba(76, 175, 80, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.position-display {
  background: rgba(129, 199, 132, 0.1);
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.position-info {
  display: flex;
  gap: 12px;
  font-size: 10px;
  margin-bottom: 4px;
}

.position-info span {
  color: #81C784;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.reset-button, .topdown-button, .iso-button {
  background: linear-gradient(45deg, #4CAF50, #66BB6A);
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.reset-button:hover, .topdown-button:hover, .iso-button:hover {
  background: linear-gradient(45deg, #43A047, #5CB85C);
  transform: translateY(-1px);
}

.controls-help {
  background: rgba(76, 175, 80, 0.05);
  padding: 8px;
  border-radius: 4px;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.help-text p {
  margin: 2px 0;
  font-size: 10px;
  color: #ccc;
}

.help-text strong {
  color: #4CAF50;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  padding: 6px 10px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 4px;
  font-size: 11px;
  color: #4CAF50;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4CAF50;
}

.status-dot.active {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
</style>
