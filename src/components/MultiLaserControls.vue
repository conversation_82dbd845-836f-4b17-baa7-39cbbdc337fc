<template>
  <div class="multi-laser-controls">
    <div class="section-header">
      <h4>🎯 多激光控制系统</h4>
      <p class="section-description">管理多个自由相机（激光）并进行远程控制</p>
    </div>

    <!-- 激光列表管理 -->
    <div class="laser-management">
      <div class="control-group">
        <div class="laser-header">
          <h5>激光列表 ({{ cameras.length }}/{{ maxCameras }})</h5>
          <button 
            @click="createNewLaser" 
            :disabled="cameras.length >= maxCameras"
            class="btn btn-primary btn-sm"
          >
            ➕ 新建激光
          </button>
        </div>
      </div>

      <!-- 激光列表 -->
      <div class="laser-list">
        <div 
          v-for="camera in cameras" 
          :key="camera.id"
          class="laser-item"
          :class="{ 
            'active': camera.isActive, 
            'visible': camera.isVisible,
            'selected': selectedLasers.includes(camera.id)
          }"
        >
          <div class="laser-info">
            <div class="laser-checkbox">
              <input 
                type="checkbox" 
                :value="camera.id"
                v-model="selectedLasers"
                @change="updateSelectedLasers"
              />
            </div>
            <div class="laser-details">
              <div class="laser-name">
                <input 
                  v-if="editingLaser === camera.id"
                  v-model="editingName"
                  @blur="finishEditing"
                  @keyup.enter="finishEditing"
                  @keyup.escape="cancelEditing"
                  class="name-input"
                  ref="nameInput"
                />
                <span v-else @dblclick="startEditing(camera.id, camera.name)">
                  {{ camera.name }}
                </span>
              </div>
              <div class="laser-status">
                <span class="status-badge" :class="{ active: camera.isActive }">
                  {{ camera.isActive ? '激活' : '待机' }}
                </span>
                <span class="status-badge" :class="{ visible: camera.isVisible }">
                  {{ camera.isVisible ? '可见' : '隐藏' }}
                </span>
                <span class="status-badge" :class="{ enabled: camera.viewportFrameEnabled }">
                  {{ camera.viewportFrameEnabled ? '蒙板' : '无蒙板' }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="laser-controls">
            <button 
              @click="activateLaser(camera.id)"
              :disabled="camera.isActive"
              class="btn btn-sm"
              title="激活激光"
            >
              🎯
            </button>
            <button 
              @click="toggleLaserVisibility(camera.id)"
              class="btn btn-sm"
              :title="camera.isVisible ? '隐藏激光' : '显示激光'"
            >
              {{ camera.isVisible ? '👁️' : '🙈' }}
            </button>
            <button 
              @click="jumpToLaser(camera.id)"
              class="btn btn-sm"
              title="跳转到激光视角"
            >
              📷
            </button>
            <button 
              @click="deleteLaser(camera.id)"
              class="btn btn-sm btn-danger"
              title="删除激光"
            >
              🗑️
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 远程控制设置 -->
    <div class="remote-control-section">
      <div class="control-group">
        <label class="toggle-label">
          <input
            type="checkbox"
            v-model="remoteControlEnabled"
            @change="toggleRemoteControl"
            class="toggle-checkbox"
          />
          <span class="toggle-text">启用远程控制</span>
        </label>
      </div>

      <div v-if="remoteControlEnabled" class="remote-control-config">
        <div class="control-group">
          <label>控制模式:</label>
          <select v-model="controlMode" @change="updateControlMode" class="select-input">
            <option value="single">单激光控制</option>
            <option value="multiple">多激光同步控制</option>
            <option value="sequential">激光轮换控制</option>
          </select>
        </div>

        <div class="selected-lasers-info">
          <h6>已选择的激光 ({{ selectedLasers.length }})</h6>
          <div class="selected-list">
            <span 
              v-for="cameraId in selectedLasers" 
              :key="cameraId"
              class="selected-tag"
            >
              {{ getCameraName(cameraId) }}
            </span>
            <span v-if="selectedLasers.length === 0" class="no-selection">
              未选择任何激光
            </span>
          </div>
        </div>

        <!-- 控制说明 -->
        <div class="control-instructions">
          <h6>控制说明:</h6>
          <ul>
            <li><strong>WASD:</strong> 移动激光位置</li>
            <li><strong>QE:</strong> 上升/下降</li>
            <li><strong>方向键:</strong> 调整激光朝向</li>
            <li><strong>Shift:</strong> 加速移动</li>
            <li><strong>Ctrl:</strong> 精确移动</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedLasers.length > 0" class="batch-operations">
      <h6>批量操作 ({{ selectedLasers.length }} 个激光)</h6>
      <div class="batch-controls">
        <button @click="batchToggleVisibility" class="btn btn-sm">
          批量切换可见性
        </button>
        <button @click="batchEnableViewportFrame" class="btn btn-sm">
          批量启用蒙板
        </button>
        <button @click="batchDisableViewportFrame" class="btn btn-sm">
          批量禁用蒙板
        </button>
        <button @click="batchResetPosition" class="btn btn-sm">
          批量重置位置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, watch } from 'vue'
import { FreeCameraInstance } from '../types/CameraTypes'

// Props
interface Props {
  cameras?: FreeCameraInstance[]
  maxCameras?: number
  onCreateLaser?: () => void
  onDeleteLaser?: (id: string) => void
  onRenameLaser?: (id: string, name: string) => void
  onActivateLaser?: (id: string) => void
  onToggleLaserVisibility?: (id: string) => void
  onJumpToLaser?: (id: string) => void
  onToggleRemoteControl?: (enabled: boolean) => void
  onUpdateControlMode?: (mode: string) => void
  onBatchOperation?: (operation: string, laserIds: string[]) => void
  onSelectedLasersUpdate?: (selectedLaserIds: string[]) => void
}

const props = withDefaults(defineProps<Props>(), {
  cameras: () => [],
  maxCameras: 10
})

// Reactive data
const selectedLasers = ref<string[]>([])
const remoteControlEnabled = ref(false)
const controlMode = ref('single')
const editingLaser = ref<string | null>(null)
const editingName = ref('')
const nameInput = ref<HTMLInputElement>()

// Computed
const cameras = computed(() => props.cameras || [])

// Methods
const createNewLaser = () => {
  props.onCreateLaser?.()
}

const deleteLaser = (id: string) => {
  if (confirm('确定要删除这个激光吗？')) {
    props.onDeleteLaser?.(id)
    // 从选中列表中移除
    const index = selectedLasers.value.indexOf(id)
    if (index > -1) {
      selectedLasers.value.splice(index, 1)
    }
  }
}

const activateLaser = (id: string) => {
  props.onActivateLaser?.(id)
}

const toggleLaserVisibility = (id: string) => {
  props.onToggleLaserVisibility?.(id)
}

const jumpToLaser = (id: string) => {
  props.onJumpToLaser?.(id)
}

const startEditing = (id: string, currentName: string) => {
  editingLaser.value = id
  editingName.value = currentName
  nextTick(() => {
    nameInput.value?.focus()
    nameInput.value?.select()
  })
}

const finishEditing = () => {
  if (editingLaser.value && editingName.value.trim()) {
    props.onRenameLaser?.(editingLaser.value, editingName.value.trim())
  }
  cancelEditing()
}

const cancelEditing = () => {
  editingLaser.value = null
  editingName.value = ''
}

const toggleRemoteControl = () => {
  props.onToggleRemoteControl?.(remoteControlEnabled.value)
}

const updateControlMode = () => {
  props.onUpdateControlMode?.(controlMode.value)
}

const updateSelectedLasers = () => {
  // 选择状态已通过 v-model 自动更新
  // 通知父组件选中的激光列表已更新
  props.onSelectedLasersUpdate?.(selectedLasers.value)
}

const getCameraName = (id: string): string => {
  const camera = cameras.value.find(c => c.id === id)
  return camera?.name || '未知激光'
}

// 批量操作
const batchToggleVisibility = () => {
  props.onBatchOperation?.('toggleVisibility', selectedLasers.value)
}

const batchEnableViewportFrame = () => {
  props.onBatchOperation?.('enableViewportFrame', selectedLasers.value)
}

const batchDisableViewportFrame = () => {
  props.onBatchOperation?.('disableViewportFrame', selectedLasers.value)
}

const batchResetPosition = () => {
  props.onBatchOperation?.('resetPosition', selectedLasers.value)
}

// Watch for selectedLasers changes
watch(selectedLasers, (newSelectedLasers) => {
  props.onSelectedLasersUpdate?.(newSelectedLasers)
}, { deep: true })

// Expose methods
defineExpose({
  selectedLasers,
  remoteControlEnabled,
  controlMode
})
</script>

<style scoped>
.multi-laser-controls {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.section-description {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 0.9em;
}

.laser-management {
  margin-bottom: 20px;
}

.laser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.laser-header h5 {
  margin: 0;
  color: #333;
}

.laser-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.laser-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.laser-item:hover {
  background-color: #f5f5f5;
}

.laser-item.active {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.laser-item.selected {
  background-color: #fff3e0;
  border-left: 4px solid #ff9800;
}

.laser-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.laser-checkbox {
  margin-right: 10px;
}

.laser-details {
  flex: 1;
}

.laser-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.name-input {
  border: 1px solid #ddd;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.9em;
}

.laser-status {
  display: flex;
  gap: 5px;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8em;
  background: #e0e0e0;
  color: #666;
}

.status-badge.active {
  background: #4caf50;
  color: white;
}

.status-badge.visible {
  background: #2196f3;
  color: white;
}

.status-badge.enabled {
  background: #ff9800;
  color: white;
}

.laser-controls {
  display: flex;
  gap: 5px;
}

.btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.9em;
}

.btn:hover {
  background: #f0f0f0;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.btn-sm {
  padding: 2px 6px;
  font-size: 0.8em;
}

.remote-control-section {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.control-group {
  margin-bottom: 10px;
}

.toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-checkbox {
  margin-right: 8px;
}

.select-input {
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.selected-lasers-info {
  margin: 15px 0;
}

.selected-lasers-info h6 {
  margin: 0 0 8px 0;
  color: #333;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.selected-tag {
  padding: 3px 8px;
  background: #e3f2fd;
  border-radius: 12px;
  font-size: 0.8em;
  color: #1976d2;
}

.no-selection {
  color: #999;
  font-style: italic;
}

.control-instructions {
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.control-instructions h6 {
  margin: 0 0 8px 0;
  color: #333;
}

.control-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.control-instructions li {
  margin-bottom: 3px;
  font-size: 0.9em;
}

.batch-operations {
  padding: 15px;
  background: #fff3e0;
  border-radius: 6px;
  border: 1px solid #ffcc02;
}

.batch-operations h6 {
  margin: 0 0 10px 0;
  color: #e65100;
}

.batch-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
