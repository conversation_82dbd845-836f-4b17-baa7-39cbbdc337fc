<template>
  <div class="remote-free-camera-controls">
    <h4>🎮 远程自由相机控制</h4>
    
    <!-- Enable Remote Control Toggle -->
    <div class="control-group">
      <label class="toggle-label">
        <input 
          type="checkbox" 
          v-model="isRemoteControlEnabled" 
          @change="toggleRemoteControl"
          class="toggle-checkbox"
        />
        <span class="toggle-text">启用远程控制</span>
      </label>
    </div>
    
    <!-- Status Display -->
    <div v-if="isRemoteControlEnabled" class="status-display">
      <h5>自由相机实时状态:</h5>
      <div class="position-info">
        <div class="coord-group">
          <span class="coord-label">位置:</span>
          <span class="coord-value">X: {{ freeCameraPosition.x.toFixed(2) }}</span>
          <span class="coord-value">Y: {{ freeCameraPosition.y.toFixed(2) }}</span>
          <span class="coord-value">Z: {{ freeCameraPosition.z.toFixed(2) }}</span>
        </div>
        <div class="coord-group">
          <span class="coord-label">朝向:</span>
          <span class="coord-value">俯仰: {{ (freeCameraRotation.x * 180 / Math.PI).toFixed(1) }}°</span>
          <span class="coord-value">偏航: {{ (freeCameraRotation.y * 180 / Math.PI).toFixed(1) }}°</span>
        </div>
      </div>
    </div>

    <!-- Control Instructions -->
    <div v-if="isRemoteControlEnabled" class="controls-help">
      <h5>键盘控制:</h5>
      <div class="help-grid">
        <div class="help-section">
          <h6>位置控制:</h6>
          <div class="help-text">
            <p><strong>W:</strong> 前进</p>
            <p><strong>S:</strong> 后退</p>
            <p><strong>A:</strong> 左移</p>
            <p><strong>D:</strong> 右移</p>
            <p><strong>Q:</strong> 上升</p>
            <p><strong>E:</strong> 下降</p>
          </div>
        </div>
        <div class="help-section">
          <h6>朝向控制:</h6>
          <div class="help-text">
            <p><strong>←:</strong> 左转</p>
            <p><strong>→:</strong> 右转</p>
            <p><strong>↑:</strong> 上仰</p>
            <p><strong>↓:</strong> 下俯</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div v-if="isRemoteControlEnabled" class="quick-actions">
      <h5>快速操作:</h5>
      <div class="action-buttons">
        <button @click="resetFreeCameraPosition" class="action-button reset">
          🔄 重置位置
        </button>
        <button @click="jumpToFreeCameraView" class="action-button jump">
          📷 跳转到自由相机视角
        </button>
        <button @click="copyCurrentPosition" class="action-button copy">
          📋 复制当前位置
        </button>
        <button @click="toggleFreeCameraVisibility" class="action-button visibility">
          {{ isFreeCameraVisible ? '👁️ 隐藏标识' : '👁️‍🗨️ 显示标识' }}
        </button>
      </div>
    </div>

    <!-- Preset Positions for Free Camera -->
    <div v-if="isRemoteControlEnabled" class="preset-section">
      <h5>自由相机预设位置:</h5>
      <div class="preset-buttons">
        <button 
          v-for="preset in freeCameraPresets" 
          :key="preset.name"
          @click="applyFreeCameraPreset(preset)"
          class="preset-button"
          :title="preset.description"
        >
          {{ preset.name }}
        </button>
      </div>
    </div>

    <!-- Manual Position Input -->
    <div v-if="isRemoteControlEnabled" class="manual-input-section">
      <h5>手动设置位置:</h5>
      <div class="input-grid">
        <div class="input-group">
          <label>X:</label>
          <input 
            type="number" 
            v-model.number="inputPosition.x" 
            step="0.1"
            @keyup.enter="applyManualPosition"
            class="number-input"
          />
        </div>
        <div class="input-group">
          <label>Y:</label>
          <input 
            type="number" 
            v-model.number="inputPosition.y" 
            step="0.1"
            @keyup.enter="applyManualPosition"
            class="number-input"
          />
        </div>
        <div class="input-group">
          <label>Z:</label>
          <input 
            type="number" 
            v-model.number="inputPosition.z" 
            step="0.1"
            @keyup.enter="applyManualPosition"
            class="number-input"
          />
        </div>
      </div>
      <div class="input-grid">
        <div class="input-group">
          <label>俯仰 (°):</label>
          <input 
            type="number" 
            v-model.number="inputRotation.pitch" 
            step="1"
            min="-72"
            max="72"
            @keyup.enter="applyManualRotation"
            class="number-input"
          />
        </div>
        <div class="input-group">
          <label>偏航 (°):</label>
          <input 
            type="number" 
            v-model.number="inputRotation.yaw" 
            step="1"
            @keyup.enter="applyManualRotation"
            class="number-input"
          />
        </div>
      </div>
      <div class="manual-buttons">
        <button @click="applyManualPosition" class="apply-button">
          📍 应用位置
        </button>
        <button @click="applyManualRotation" class="apply-button">
          🎯 应用朝向
        </button>
      </div>
    </div>

    <!-- Status Indicator -->
    <div v-if="isRemoteControlEnabled" class="status-indicator">
      <div class="status-dot active"></div>
      <span>远程控制已激活 - 使用WASD+QE和方向键控制自由相机</span>
    </div>

    <!-- Warning -->
    <div v-if="!isRemoteControlEnabled" class="warning-message">
      <p>💡 提示：启用远程控制后，您可以在轨道相机视角下使用WASD+QE和方向键控制自由相机的位置和朝向。</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { CameraPreset } from '../types/CameraTypes'

// Props
interface Props {
  onToggleRemoteControl?: (enabled: boolean) => void
  onResetFreeCameraPosition?: () => void
  onJumpToFreeCameraView?: () => void
  onToggleFreeCameraVisibility?: () => void
  onApplyFreeCameraPreset?: (preset: CameraPreset) => void
  onSetFreeCameraPosition?: (x: number, y: number, z: number) => void
  onSetFreeCameraRotation?: (x: number, y: number, z: number) => void
}

const props = defineProps<Props>()

// Reactive state
const isRemoteControlEnabled = ref(false)
const isFreeCameraVisible = ref(true)

const freeCameraPosition = reactive({
  x: 10,
  y: 10,
  z: 10
})

const freeCameraRotation = reactive({
  x: 0,
  y: 0,
  z: 0
})

// Manual input
const inputPosition = reactive({
  x: 10,
  y: 10,
  z: 10
})

const inputRotation = reactive({
  pitch: 0,
  yaw: 0
})

// Free camera presets
const freeCameraPresets: CameraPreset[] = [
  {
    name: '🏠 默认',
    position: { x: 10, y: 10, z: 10 },
    rotation: { x: 0, y: 0, z: 0 },
    description: '默认自由相机位置'
  },
  {
    name: '🚗 车辆上方',
    position: { x: 0, y: 15, z: 0 },
    rotation: { x: -Math.PI / 2, y: 0, z: 0 },
    description: '车辆正上方俯视'
  },
  {
    name: '🌅 车辆前方',
    position: { x: 0, y: 5, z: 20 },
    rotation: { x: 0, y: Math.PI, z: 0 },
    description: '车辆前方观察'
  },
  {
    name: '🔍 近距离',
    position: { x: 5, y: 3, z: 5 },
    rotation: { x: -0.2, y: -Math.PI / 4, z: 0 },
    description: '近距离观察细节'
  },
  {
    name: '🌄 远景',
    position: { x: 30, y: 20, z: 30 },
    rotation: { x: -0.3, y: -Math.PI / 4, z: 0 },
    description: '远距离全景观察'
  },
  {
    name: '🎬 电影视角',
    position: { x: -10, y: 8, z: 15 },
    rotation: { x: -0.1, y: Math.PI / 6, z: 0 },
    description: '电影般的观察角度'
  }
]

// Methods
const toggleRemoteControl = () => {
  props.onToggleRemoteControl?.(isRemoteControlEnabled.value)
}

const resetFreeCameraPosition = () => {
  props.onResetFreeCameraPosition?.()
}

const jumpToFreeCameraView = () => {
  props.onJumpToFreeCameraView?.()
}

const toggleFreeCameraVisibility = () => {
  isFreeCameraVisible.value = !isFreeCameraVisible.value
  props.onToggleFreeCameraVisibility?.()
}

const applyFreeCameraPreset = (preset: CameraPreset) => {
  props.onApplyFreeCameraPreset?.(preset)
  // 同步到输入框
  inputPosition.x = preset.position.x
  inputPosition.y = preset.position.y
  inputPosition.z = preset.position.z
  inputRotation.pitch = preset.rotation.x * 180 / Math.PI
  inputRotation.yaw = preset.rotation.y * 180 / Math.PI
}

const applyManualPosition = () => {
  props.onSetFreeCameraPosition?.(inputPosition.x, inputPosition.y, inputPosition.z)
}

const applyManualRotation = () => {
  const pitchRad = inputRotation.pitch * Math.PI / 180
  const yawRad = inputRotation.yaw * Math.PI / 180
  props.onSetFreeCameraRotation?.(pitchRad, yawRad, 0)
}

const copyCurrentPosition = () => {
  const positionText = `位置: (${freeCameraPosition.x.toFixed(2)}, ${freeCameraPosition.y.toFixed(2)}, ${freeCameraPosition.z.toFixed(2)})\n朝向: (${(freeCameraRotation.x * 180 / Math.PI).toFixed(1)}°, ${(freeCameraRotation.y * 180 / Math.PI).toFixed(1)}°)`
  
  if (navigator.clipboard) {
    navigator.clipboard.writeText(positionText).then(() => {
      alert('位置信息已复制到剪贴板')
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = positionText
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert('位置信息已复制到剪贴板')
    })
  }
}

// Expose methods for parent component
const updateFreeCameraPosition = (x: number, y: number, z: number) => {
  freeCameraPosition.x = x
  freeCameraPosition.y = y
  freeCameraPosition.z = z
  // 同步到输入框
  inputPosition.x = x
  inputPosition.y = y
  inputPosition.z = z
}

const updateFreeCameraRotation = (x: number, y: number, z: number) => {
  freeCameraRotation.x = x
  freeCameraRotation.y = y
  freeCameraRotation.z = z
  // 同步到输入框（转换为度）
  inputRotation.pitch = x * 180 / Math.PI
  inputRotation.yaw = y * 180 / Math.PI
}

const setRemoteControlEnabled = (enabled: boolean) => {
  isRemoteControlEnabled.value = enabled
}

const setFreeCameraVisible = (visible: boolean) => {
  isFreeCameraVisible.value = visible
}

defineExpose({
  updateFreeCameraPosition,
  updateFreeCameraRotation,
  setRemoteControlEnabled,
  setFreeCameraVisible
})
</script>

<style scoped>
.remote-free-camera-controls {
  background: rgba(156, 39, 176, 0.1);
  border: 1px solid rgba(156, 39, 176, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  font-size: 11px;
}

.remote-free-camera-controls h4 {
  margin: 0 0 12px 0;
  color: #9C27B0;
  font-size: 12px;
  font-weight: bold;
}

.remote-free-camera-controls h5 {
  margin: 8px 0 6px 0;
  color: #9C27B0;
  font-size: 10px;
  font-weight: bold;
}

.remote-free-camera-controls h6 {
  margin: 4px 0 2px 0;
  color: #BA68C8;
  font-size: 9px;
  font-weight: bold;
}

.control-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 8px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.toggle-checkbox {
  width: 12px;
  height: 12px;
  accent-color: #9C27B0;
}

.toggle-text {
  font-size: 9px;
  color: #BA68C8;
}

.status-display {
  background: rgba(186, 104, 200, 0.1);
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.position-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.coord-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.coord-label {
  font-size: 9px;
  color: #9C27B0;
  font-weight: bold;
  min-width: 30px;
}

.coord-value {
  font-size: 9px;
  color: #BA68C8;
  font-weight: bold;
  background: rgba(156, 39, 176, 0.1);
  padding: 2px 4px;
  border-radius: 2px;
}

.controls-help {
  background: rgba(186, 104, 200, 0.1);
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.help-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.help-section {
  background: rgba(156, 39, 176, 0.1);
  padding: 6px;
  border-radius: 3px;
}

.help-text {
  font-size: 8px;
}

.help-text p {
  margin: 1px 0;
  color: #BA68C8;
}

.help-text strong {
  color: #9C27B0;
}

.quick-actions {
  background: rgba(156, 39, 176, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.action-button {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  transition: all 0.2s;
}

.action-button.reset {
  background: #FF9800;
  color: white;
}

.action-button.reset:hover {
  background: #F57C00;
}

.action-button.jump {
  background: #2196F3;
  color: white;
}

.action-button.jump:hover {
  background: #1976D2;
}

.action-button.copy {
  background: #4CAF50;
  color: white;
}

.action-button.copy:hover {
  background: #388E3C;
}

.action-button.visibility {
  background: #9C27B0;
  color: white;
}

.action-button.visibility:hover {
  background: #7B1FA2;
}

.preset-section {
  background: rgba(156, 39, 176, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.preset-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.preset-button {
  padding: 6px 8px;
  background: rgba(156, 39, 176, 0.2);
  color: #BA68C8;
  border: 1px solid #9C27B0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  transition: all 0.2s;
}

.preset-button:hover {
  background: rgba(156, 39, 176, 0.4);
  color: #9C27B0;
  transform: translateY(-1px);
}

.manual-input-section {
  background: rgba(156, 39, 176, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.input-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 8px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.input-group label {
  font-size: 9px;
  color: #9C27B0;
  font-weight: bold;
}

.number-input {
  padding: 4px;
  border: 1px solid #9C27B0;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.3);
  color: #BA68C8;
  font-size: 10px;
  width: 100%;
}

.number-input:focus {
  outline: none;
  border-color: #BA68C8;
  background: rgba(0, 0, 0, 0.5);
}

.manual-buttons {
  display: flex;
  gap: 6px;
}

.apply-button {
  padding: 6px 12px;
  background: #9C27B0;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  font-weight: bold;
  transition: background-color 0.2s;
  flex: 1;
}

.apply-button:hover {
  background: #7B1FA2;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  background: rgba(156, 39, 176, 0.2);
  border: 1px solid rgba(156, 39, 176, 0.3);
  border-radius: 4px;
  font-size: 10px;
  color: #9C27B0;
  margin-bottom: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #9C27B0;
}

.status-dot.active {
  animation: pulse-purple 2s infinite;
}

@keyframes pulse-purple {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.warning-message {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 4px;
  padding: 8px;
  color: #FFC107;
  font-size: 10px;
}

.warning-message p {
  margin: 0;
}
</style>
