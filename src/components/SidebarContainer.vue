<template>
  <div class="sidebar-container" :class="{ 'collapsed': isCollapsed }">
    <!-- Toggle Button -->
    <button 
      class="sidebar-toggle" 
      @click="toggleSidebar"
      :title="isCollapsed ? '展开控制面板' : '收起控制面板'"
    >
      <div class="hamburger-icon" :class="{ 'active': !isCollapsed }">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </button>

    <!-- Sidebar Content -->
    <div class="sidebar-content" v-show="!isCollapsed">
      <!-- Tab Navigation -->
      <div class="tab-navigation">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          class="tab-button"
          :class="{ 'active': activeTab === tab.id }"
          @click="setActiveTab(tab.id)"
          :title="tab.title"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-label">{{ tab.label }}</span>
        </button>
      </div>

      <!-- Tab Content Area -->
      <div class="tab-content-area">
        <div class="tab-content" :class="{ 'active': activeTab === 'camera' }">
          <div class="tab-header">
            <h3>📷 相机控制</h3>
            <p>自由相机和全局相机设置</p>
          </div>
          <div class="tab-body">
            <slot name="camera-controls"></slot>
          </div>
        </div>

        <div class="tab-content" :class="{ 'active': activeTab === 'remote' }">
          <div class="tab-header">
            <h3>🎮 远程控制</h3>
            <p>远程自由相机控制</p>
          </div>
          <div class="tab-body">
            <slot name="remote-controls"></slot>
          </div>
        </div>

        <div class="tab-content" :class="{ 'active': activeTab === 'fov' }">
          <div class="tab-header">
            <h3>🎛️ 视野设置</h3>
            <p>FOV和视角控制</p>
          </div>
          <div class="tab-body">
            <slot name="fov-controls"></slot>
          </div>
        </div>

        <div class="tab-content" :class="{ 'active': activeTab === 'settings' }">
          <div class="tab-header">
            <h3>⚙️ 系统设置</h3>
            <p>其他配置选项</p>
          </div>
          <div class="tab-body">
            <slot name="system-settings"></slot>
          </div>
        </div>
      </div>
    </div>

    <!-- Collapsed State Indicator -->
    <div class="collapsed-indicator" v-show="isCollapsed">
      <div class="indicator-dots">
        <div 
          v-for="tab in tabs" 
          :key="tab.id"
          class="indicator-dot"
          :class="{ 'active': activeTab === tab.id }"
          @click="expandAndSetTab(tab.id)"
          :title="tab.title"
        >
          {{ tab.icon }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

// Props
interface Props {
  defaultTab?: string
  rememberState?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultTab: 'camera',
  rememberState: true
})

// Reactive state
const isCollapsed = ref(false)
const activeTab = ref(props.defaultTab)

// Tab configuration
const tabs = [
  {
    id: 'camera',
    icon: '📷',
    label: '相机控制',
    title: '自由相机和全局相机设置'
  },
  {
    id: 'remote',
    icon: '🎮',
    label: '远程控制',
    title: '远程自由相机控制'
  },
  {
    id: 'fov',
    icon: '🎛️',
    label: '视野设置',
    title: 'FOV和视角控制'
  },
  {
    id: 'settings',
    icon: '⚙️',
    label: '系统设置',
    title: '其他配置选项'
  }
]

// Methods
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
  if (props.rememberState) {
    localStorage.setItem('sidebar-collapsed', isCollapsed.value.toString())
  }
}

const setActiveTab = (tabId: string) => {
  activeTab.value = tabId
  if (props.rememberState) {
    localStorage.setItem('sidebar-active-tab', tabId)
  }
}

const expandAndSetTab = (tabId: string) => {
  isCollapsed.value = false
  setActiveTab(tabId)
  if (props.rememberState) {
    localStorage.setItem('sidebar-collapsed', 'false')
  }
}

// Load saved state
onMounted(() => {
  if (props.rememberState) {
    const savedCollapsed = localStorage.getItem('sidebar-collapsed')
    const savedTab = localStorage.getItem('sidebar-active-tab')
    
    if (savedCollapsed !== null) {
      isCollapsed.value = savedCollapsed === 'true'
    }
    
    if (savedTab) {
      activeTab.value = savedTab
    }
  }
})

// Expose methods for parent component
defineExpose({
  toggleSidebar,
  setActiveTab,
  expandAndSetTab,
  isCollapsed,
  activeTab
})
</script>

<style scoped>
.sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 320px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
}

.sidebar-container.collapsed {
  width: 60px;
}

/* Toggle Button */
.sidebar-toggle {
  position: absolute;
  top: 20px;
  right: -40px;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1001;
}

.sidebar-toggle:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Hamburger Icon */
.hamburger-icon {
  width: 20px;
  height: 16px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.3s ease-in-out;
}

.hamburger-icon span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: #fff;
  border-radius: 1px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.hamburger-icon span:nth-child(1) {
  top: 0px;
}

.hamburger-icon span:nth-child(2) {
  top: 7px;
}

.hamburger-icon span:nth-child(3) {
  top: 14px;
}

.hamburger-icon.active span:nth-child(1) {
  top: 7px;
  transform: rotate(135deg);
}

.hamburger-icon.active span:nth-child(2) {
  opacity: 0;
  left: -20px;
}

.hamburger-icon.active span:nth-child(3) {
  top: 7px;
  transform: rotate(-135deg);
}

/* Sidebar Content */
.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-navigation::-webkit-scrollbar {
  display: none;
}

.tab-button {
  flex: 1;
  min-width: 80px;
  padding: 12px 8px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  position: relative;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.tab-button.active {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #4CAF50, #2196F3);
}

.tab-icon {
  font-size: 16px;
  line-height: 1;
}

.tab-label {
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
}

/* Tab Content Area */
.tab-content-area {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.tab-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.3s ease;
  pointer-events: none;
  display: flex;
  flex-direction: column;
}

.tab-content.active {
  opacity: 1;
  transform: translateX(0);
  pointer-events: all;
}

.tab-header {
  padding: 20px 16px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.tab-header h3 {
  margin: 0 0 4px 0;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.tab-header p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.tab-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.tab-body::-webkit-scrollbar {
  width: 6px;
}

.tab-body::-webkit-scrollbar-track {
  background: transparent;
}

.tab-body::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.tab-body::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Collapsed State Indicator */
.collapsed-indicator {
  padding: 20px 0;
  display: flex;
  justify-content: center;
}

.indicator-dots {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.indicator-dot {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.indicator-dot:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.indicator-dot.active {
  background: rgba(76, 175, 80, 0.3);
  border-color: #4CAF50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar-container {
    width: 280px;
  }
  
  .sidebar-container.collapsed {
    width: 50px;
  }
  
  .tab-button {
    min-width: 70px;
    padding: 10px 6px;
  }
  
  .tab-label {
    font-size: 9px;
  }
  
  .tab-body {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .sidebar-container {
    width: 100vw;
  }
  
  .sidebar-container.collapsed {
    width: 40px;
  }
}
</style>
