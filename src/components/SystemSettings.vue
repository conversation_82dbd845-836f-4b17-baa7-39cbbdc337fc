<template>
  <div class="system-settings">
    <!-- Performance Settings -->
    <div class="settings-section">
      <h5>🚀 性能设置</h5>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="performanceSettings.enableVSync" 
            @change="updatePerformanceSettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">启用垂直同步</span>
        </label>
      </div>
      
      <div class="control-group">
        <label>渲染质量:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="performanceSettings.renderQuality"
            min="0.5"
            max="2.0"
            step="0.1"
            @input="updatePerformanceSettings"
            class="slider"
          />
          <span class="value">{{ performanceSettings.renderQuality.toFixed(1) }}x</span>
        </div>
      </div>
      
      <div class="control-group">
        <label>阴影质量:</label>
        <select 
          v-model="performanceSettings.shadowQuality" 
          @change="updatePerformanceSettings"
          class="select-input"
        >
          <option value="low">低</option>
          <option value="medium">中</option>
          <option value="high">高</option>
          <option value="ultra">超高</option>
        </select>
      </div>
    </div>

    <!-- Display Settings -->
    <div class="settings-section">
      <h5>🖥️ 显示设置</h5>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="displaySettings.showFPS" 
            @change="updateDisplaySettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">显示FPS计数器</span>
        </label>
      </div>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="displaySettings.showGrid" 
            @change="updateDisplaySettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">显示网格</span>
        </label>
      </div>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="displaySettings.showAxes" 
            @change="updateDisplaySettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">显示坐标轴</span>
        </label>
      </div>
      
      <div class="control-group">
        <label>背景颜色:</label>
        <input 
          type="color" 
          v-model="displaySettings.backgroundColor" 
          @change="updateDisplaySettings"
          class="color-input"
        />
      </div>
    </div>

    <!-- Control Settings -->
    <div class="settings-section">
      <h5>🎮 控制设置</h5>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="controlSettings.invertMouseY" 
            @change="updateControlSettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">反转鼠标Y轴</span>
        </label>
      </div>
      
      <div class="control-group">
        <label>鼠标灵敏度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="controlSettings.mouseSensitivity"
            min="0.1"
            max="5.0"
            step="0.1"
            @input="updateControlSettings"
            class="slider"
          />
          <span class="value">{{ controlSettings.mouseSensitivity.toFixed(1) }}</span>
        </div>
      </div>
      
      <div class="control-group">
        <label>键盘移动速度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="controlSettings.keyboardSpeed"
            min="1"
            max="20"
            step="1"
            @input="updateControlSettings"
            class="slider"
          />
          <span class="value">{{ controlSettings.keyboardSpeed }}</span>
        </div>
      </div>
    </div>

    <!-- Storage Settings -->
    <div class="settings-section">
      <h5>💾 存储设置</h5>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="storageSettings.autoSave" 
            @change="updateStorageSettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">自动保存设置</span>
        </label>
      </div>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="storageSettings.rememberCameraPosition" 
            @change="updateStorageSettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">记住相机位置</span>
        </label>
      </div>
      
      <div class="action-buttons">
        <button @click="exportSettings" class="action-button export">
          📤 导出设置
        </button>
        <button @click="importSettings" class="action-button import">
          📥 导入设置
        </button>
        <button @click="resetAllSettings" class="action-button reset">
          🔄 重置所有设置
        </button>
      </div>
    </div>

    <!-- Debug Settings -->
    <div class="settings-section">
      <h5>🐛 调试设置</h5>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="debugSettings.enableDebugMode" 
            @change="updateDebugSettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">启用调试模式</span>
        </label>
      </div>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="debugSettings.showConsoleLog" 
            @change="updateDebugSettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">显示控制台日志</span>
        </label>
      </div>
      
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="debugSettings.showWireframe" 
            @change="updateDebugSettings"
            class="toggle-checkbox"
          />
          <span class="toggle-text">显示线框模式</span>
        </label>
      </div>
    </div>

    <!-- System Info -->
    <div class="settings-section">
      <h5>ℹ️ 系统信息</h5>
      
      <div class="info-grid">
        <div class="info-item">
          <span class="info-label">浏览器:</span>
          <span class="info-value">{{ systemInfo.browser }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">WebGL版本:</span>
          <span class="info-value">{{ systemInfo.webglVersion }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">GPU:</span>
          <span class="info-value">{{ systemInfo.gpu }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">屏幕分辨率:</span>
          <span class="info-value">{{ systemInfo.screenResolution }}</span>
        </div>
      </div>
    </div>

    <!-- Hidden file input for import -->
    <input 
      ref="fileInput" 
      type="file" 
      accept=".json" 
      @change="handleFileImport" 
      style="display: none"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// Props
interface Props {
  onSettingsChange?: (category: string, settings: any) => void
}

const props = defineProps<Props>()

// Reactive settings
const performanceSettings = reactive({
  enableVSync: true,
  renderQuality: 1.0,
  shadowQuality: 'medium'
})

const displaySettings = reactive({
  showFPS: false,
  showGrid: false,
  showAxes: false,
  backgroundColor: '#000000'
})

const controlSettings = reactive({
  invertMouseY: false,
  mouseSensitivity: 1.0,
  keyboardSpeed: 5
})

const storageSettings = reactive({
  autoSave: true,
  rememberCameraPosition: true
})

const debugSettings = reactive({
  enableDebugMode: false,
  showConsoleLog: false,
  showWireframe: false
})

const systemInfo = reactive({
  browser: '',
  webglVersion: '',
  gpu: '',
  screenResolution: ''
})

// File input reference
const fileInput = ref<HTMLInputElement>()

// Methods
const updatePerformanceSettings = () => {
  props.onSettingsChange?.('performance', performanceSettings)
  if (storageSettings.autoSave) {
    localStorage.setItem('performance-settings', JSON.stringify(performanceSettings))
  }
}

const updateDisplaySettings = () => {
  props.onSettingsChange?.('display', displaySettings)
  if (storageSettings.autoSave) {
    localStorage.setItem('display-settings', JSON.stringify(displaySettings))
  }
}

const updateControlSettings = () => {
  props.onSettingsChange?.('control', controlSettings)
  if (storageSettings.autoSave) {
    localStorage.setItem('control-settings', JSON.stringify(controlSettings))
  }
}

const updateStorageSettings = () => {
  props.onSettingsChange?.('storage', storageSettings)
  localStorage.setItem('storage-settings', JSON.stringify(storageSettings))
}

const updateDebugSettings = () => {
  props.onSettingsChange?.('debug', debugSettings)
  if (storageSettings.autoSave) {
    localStorage.setItem('debug-settings', JSON.stringify(debugSettings))
  }
}

const exportSettings = () => {
  const allSettings = {
    performance: performanceSettings,
    display: displaySettings,
    control: controlSettings,
    storage: storageSettings,
    debug: debugSettings,
    exportDate: new Date().toISOString()
  }
  
  const dataStr = JSON.stringify(allSettings, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `camera-settings-${new Date().toISOString().split('T')[0]}.json`
  link.click()
  
  URL.revokeObjectURL(url)
}

const importSettings = () => {
  fileInput.value?.click()
}

const handleFileImport = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const settings = JSON.parse(e.target?.result as string)
      
      if (settings.performance) Object.assign(performanceSettings, settings.performance)
      if (settings.display) Object.assign(displaySettings, settings.display)
      if (settings.control) Object.assign(controlSettings, settings.control)
      if (settings.storage) Object.assign(storageSettings, settings.storage)
      if (settings.debug) Object.assign(debugSettings, settings.debug)
      
      // Trigger updates
      updatePerformanceSettings()
      updateDisplaySettings()
      updateControlSettings()
      updateStorageSettings()
      updateDebugSettings()
      
      alert('设置导入成功！')
    } catch (error) {
      alert('设置文件格式错误！')
    }
  }
  reader.readAsText(file)
}

const resetAllSettings = () => {
  if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
    // Reset to defaults
    Object.assign(performanceSettings, {
      enableVSync: true,
      renderQuality: 1.0,
      shadowQuality: 'medium'
    })
    
    Object.assign(displaySettings, {
      showFPS: false,
      showGrid: false,
      showAxes: false,
      backgroundColor: '#000000'
    })
    
    Object.assign(controlSettings, {
      invertMouseY: false,
      mouseSensitivity: 1.0,
      keyboardSpeed: 5
    })
    
    Object.assign(storageSettings, {
      autoSave: true,
      rememberCameraPosition: true
    })
    
    Object.assign(debugSettings, {
      enableDebugMode: false,
      showConsoleLog: false,
      showWireframe: false
    })
    
    // Clear localStorage
    localStorage.removeItem('performance-settings')
    localStorage.removeItem('display-settings')
    localStorage.removeItem('control-settings')
    localStorage.removeItem('debug-settings')
    
    // Trigger updates
    updatePerformanceSettings()
    updateDisplaySettings()
    updateControlSettings()
    updateDebugSettings()
    
    alert('所有设置已重置为默认值！')
  }
}

const loadSystemInfo = () => {
  // Browser detection
  const userAgent = navigator.userAgent
  let browser = 'Unknown'
  if (userAgent.includes('Chrome')) browser = 'Chrome'
  else if (userAgent.includes('Firefox')) browser = 'Firefox'
  else if (userAgent.includes('Safari')) browser = 'Safari'
  else if (userAgent.includes('Edge')) browser = 'Edge'
  
  systemInfo.browser = browser
  systemInfo.screenResolution = `${screen.width}x${screen.height}`
  
  // WebGL info (simplified)
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    if (gl) {
      systemInfo.webglVersion = 'WebGL 1.0'
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        systemInfo.gpu = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || 'Unknown'
      }
    }
  } catch (e) {
    systemInfo.webglVersion = 'Not supported'
    systemInfo.gpu = 'Unknown'
  }
}

const loadSavedSettings = () => {
  try {
    const savedPerformance = localStorage.getItem('performance-settings')
    if (savedPerformance) {
      Object.assign(performanceSettings, JSON.parse(savedPerformance))
    }
    
    const savedDisplay = localStorage.getItem('display-settings')
    if (savedDisplay) {
      Object.assign(displaySettings, JSON.parse(savedDisplay))
    }
    
    const savedControl = localStorage.getItem('control-settings')
    if (savedControl) {
      Object.assign(controlSettings, JSON.parse(savedControl))
    }
    
    const savedStorage = localStorage.getItem('storage-settings')
    if (savedStorage) {
      Object.assign(storageSettings, JSON.parse(savedStorage))
    }
    
    const savedDebug = localStorage.getItem('debug-settings')
    if (savedDebug) {
      Object.assign(debugSettings, JSON.parse(savedDebug))
    }
  } catch (error) {
    console.warn('Failed to load saved settings:', error)
  }
}

// Initialize
onMounted(() => {
  loadSystemInfo()
  loadSavedSettings()
})

// Expose methods
defineExpose({
  exportSettings,
  importSettings,
  resetAllSettings
})
</script>

<style scoped>
.system-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-section {
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-section h5 {
  margin: 0 0 10px 0;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
}

.control-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 8px;
}

.control-group label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  min-width: 80px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  min-width: auto !important;
}

.toggle-checkbox {
  width: 12px;
  height: 12px;
  accent-color: #4CAF50;
}

.toggle-text {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.slider {
  flex: 1;
  max-width: 100px;
  height: 4px;
  background: #333;
  border-radius: 2px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: #4CAF50;
  border-radius: 50%;
  cursor: pointer;
}

.value {
  font-size: 10px;
  color: #4CAF50;
  font-weight: bold;
  min-width: 30px;
  text-align: right;
}

.select-input {
  padding: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
}

.color-input {
  width: 40px;
  height: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  background: transparent;
  cursor: pointer;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  font-weight: bold;
  transition: all 0.2s;
}

.action-button.export {
  background: #2196F3;
  color: white;
}

.action-button.export:hover {
  background: #1976D2;
}

.action-button.import {
  background: #FF9800;
  color: white;
}

.action-button.import:hover {
  background: #F57C00;
}

.action-button.reset {
  background: #F44336;
  color: white;
}

.action-button.reset:hover {
  background: #D32F2F;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.info-value {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
}
</style>
