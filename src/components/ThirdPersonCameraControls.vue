<template>
  <div class="third-person-camera-controls">
    <h4>🎮 第三人称相机控制</h4>
    
    <!-- Camera Mode Toggle -->
    <div class="control-group">
      <label class="toggle-label">
        <input 
          type="checkbox" 
          v-model="isThirdPersonActive" 
          @change="toggleThirdPersonMode"
          class="toggle-checkbox"
        />
        <span class="toggle-text">启用第三人称相机</span>
      </label>
    </div>
    
    <!-- Camera Configuration -->
    <div v-if="isThirdPersonActive" class="config-section">
      <!-- Follow Distance -->
      <div class="control-group">
        <label>跟随距离:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="cameraConfig.followDistance"
            min="3"
            max="50"
            step="0.5"
            @input="updateConfig"
            class="slider"
          />
          <span class="value">{{ cameraConfig.followDistance.toFixed(1) }}</span>
        </div>
      </div>
      
      <!-- Follow Height -->
      <div class="control-group">
        <label>跟随高度:</label>
        <div class="slider-container">
          <input
            type="range"
            v-model="cameraConfig.followHeight"
            min="0"
            max="20"
            step="0.5"
            @input="updateConfig"
            class="slider"
          />
          <span class="value">{{ cameraConfig.followHeight.toFixed(1) }}</span>
        </div>
      </div>
      
      <!-- Mouse Control -->
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="cameraConfig.enableMouseControl" 
            @change="updateConfig"
            class="toggle-checkbox"
          />
          <span class="toggle-text">启用鼠标控制</span>
        </label>
      </div>
      
      <!-- Smooth Follow -->
      <div class="control-group">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="cameraConfig.smoothFollow" 
            @change="updateConfig"
            class="toggle-checkbox"
          />
          <span class="toggle-text">平滑跟随</span>
        </label>
      </div>
    </div>

    <!-- Camera Status Display -->
    <div v-if="isThirdPersonActive" class="status-display">
      <h5>当前状态:</h5>
      <div class="status-info">
        <span>距离: {{ cameraPosition.distance.toFixed(1) }}</span>
        <span>高度: {{ cameraPosition.height.toFixed(1) }}</span>
      </div>
      <div class="status-info">
        <span>跟随: {{ isFollowing ? '✅' : '❌' }}</span>
        <span>目标: {{ targetInfo }}</span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div v-if="isThirdPersonActive" class="action-buttons">
      <button @click="toggleFollowing" class="follow-button">
        {{ isFollowing ? '🔓 停止跟随' : '🔒 开始跟随' }}
      </button>
      <button @click="resetToDefault" class="reset-button">
        🔄 重置视角
      </button>
      <button @click="focusOnVehicle" class="focus-button">
        🚗 聚焦车辆
      </button>
    </div>

    <!-- Controls Help -->
    <div v-if="isThirdPersonActive" class="controls-help">
      <h5>控制说明:</h5>
      <div class="help-text">
        <p><strong>键盘控制:</strong></p>
        <p><strong>WASD:</strong> 移动目标位置</p>
        <p><strong>Q/E:</strong> 上升/下降目标</p>
        <p><strong>←/→:</strong> 水平旋转</p>
        <p v-if="cameraConfig.enableMouseControl"><strong>鼠标控制:</strong></p>
        <p v-if="cameraConfig.enableMouseControl"><strong>左键拖拽:</strong> 旋转视角</p>
        <p v-if="cameraConfig.enableMouseControl"><strong>滚轮:</strong> 缩放距离</p>
      </div>
    </div>

    <!-- Status Indicator -->
    <div v-if="isThirdPersonActive" class="status-indicator">
      <div class="status-dot active"></div>
      <span>第三人称视角已激活</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ThirdPersonCameraConfig, ThirdPersonPreset } from '../types/CameraTypes'

// Props
interface Props {
  onToggleThirdPerson?: (active: boolean) => void
  onConfigUpdate?: (config: Partial<ThirdPersonCameraConfig>) => void
  onSetDistance?: (distance: number) => void
  onSetHeight?: (height: number) => void
  onSetAngle?: (horizontal: number, vertical: number) => void
  onToggleFollowing?: (following: boolean) => void
  onApplyPreset?: (preset: ThirdPersonPreset) => void
  onResetToDefault?: () => void
  onFocusOnVehicle?: () => void
}

const props = defineProps<Props>()

// Reactive state
const isThirdPersonActive = ref(false)
const isFollowing = ref(true)
const targetInfo = ref('车辆')

const cameraConfig = reactive<ThirdPersonCameraConfig>({
  fov: 50,
  near: 1,
  far: 1000,
  followDistance: 15,
  followHeight: 8,
  followAngle: 0,
  verticalAngle: -0.3,
  enableMouseControl: true,
  mouseSensitivity: 0.003,
  smoothFollow: true,
  smoothFactor: 0.1,
  moveSpeed: 10,
  rotateSpeed: 1,
  zoomSpeed: 2,
  minDistance: 3,
  maxDistance: 50,
  minVerticalAngle: -Math.PI / 2,
  maxVerticalAngle: Math.PI / 3
})

const cameraPosition = reactive({
  distance: 15,
  height: 8,
  horizontalAngle: 0,
  verticalAngle: -17
})

// Methods
const toggleThirdPersonMode = () => {
  props.onToggleThirdPerson?.(isThirdPersonActive.value)
}

const updateConfig = () => {
  props.onConfigUpdate?.(cameraConfig)
}

const toggleFollowing = () => {
  isFollowing.value = !isFollowing.value
  props.onToggleFollowing?.(isFollowing.value)
}

const resetToDefault = () => {
  props.onResetToDefault?.()
}

const focusOnVehicle = () => {
  props.onFocusOnVehicle?.()
}

// Expose methods for parent component
const updatePosition = (distance: number, height: number, horizontalAngle: number, verticalAngle: number) => {
  cameraPosition.distance = distance
  cameraPosition.height = height
  cameraPosition.horizontalAngle = horizontalAngle * 180 / Math.PI
  cameraPosition.verticalAngle = verticalAngle * 180 / Math.PI
}

const setConfig = (config: ThirdPersonCameraConfig) => {
  Object.assign(cameraConfig, config)
}

const setFollowing = (following: boolean) => {
  isFollowing.value = following
}

const setTargetInfo = (info: string) => {
  targetInfo.value = info
}

defineExpose({
  updatePosition,
  setConfig,
  setFollowing,
  setTargetInfo
})
</script>

<style scoped>
.third-person-camera-controls {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  font-size: 11px;
}

.third-person-camera-controls h4 {
  margin: 0 0 12px 0;
  color: #2196F3;
  font-size: 12px;
  font-weight: bold;
}

.third-person-camera-controls h5 {
  margin: 8px 0 6px 0;
  color: #2196F3;
  font-size: 10px;
  font-weight: bold;
}

.control-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 8px;
}

.control-group label {
  font-size: 9px;
  color: #2196F3;
  font-weight: bold;
  min-width: 60px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  min-width: auto !important;
}

.toggle-checkbox {
  width: 12px;
  height: 12px;
  accent-color: #2196F3;
}

.toggle-text {
  font-size: 9px;
  color: #64B5F6;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.slider {
  flex: 1;
  max-width: 120px;
  height: 4px;
  background: #333;
  border-radius: 2px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: #2196F3;
  border-radius: 50%;
  cursor: pointer;
}

.value {
  font-size: 10px;
  color: #2196F3;
  font-weight: bold;
  min-width: 35px;
  text-align: right;
}

.config-section {
  background: rgba(33, 150, 243, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.status-display {
  background: rgba(100, 181, 246, 0.1);
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.status-info {
  display: flex;
  gap: 12px;
  font-size: 10px;
  margin-bottom: 4px;
}

.status-info span {
  color: #64B5F6;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.follow-button, .reset-button, .focus-button {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  font-weight: bold;
  transition: all 0.2s;
}

.follow-button {
  background: #FF9800;
  color: white;
}

.follow-button:hover {
  background: #F57C00;
}

.reset-button {
  background: #9E9E9E;
  color: white;
}

.reset-button:hover {
  background: #757575;
}

.focus-button {
  background: #4CAF50;
  color: white;
}

.focus-button:hover {
  background: #388E3C;
}

.controls-help {
  background: rgba(100, 181, 246, 0.1);
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.help-text {
  font-size: 9px;
}

.help-text p {
  margin: 2px 0;
  color: #64B5F6;
}

.help-text strong {
  color: #2196F3;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  background: rgba(33, 150, 243, 0.2);
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 4px;
  font-size: 11px;
  color: #2196F3;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #2196F3;
}

.status-dot.active {
  animation: pulse-blue 2s infinite;
}

@keyframes pulse-blue {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
</style>
