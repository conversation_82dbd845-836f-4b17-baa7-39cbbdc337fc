<template>
  <div ref="container" class="three-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ThreeManager } from '../utils/ThreeManager'

const container = ref<HTMLDivElement>()
let threeManager: ThreeManager | null = null

onMounted(() => {
  if (container.value) {
    threeManager = new ThreeManager(container.value)
    threeManager.init()
  }
})

onUnmounted(() => {
  if (threeManager) {
    threeManager.dispose()
  }
})

// Expose ThreeManager for parent component access
const getThreeManager = () => threeManager

defineExpose({
  getThreeManager
})
</script>

<style scoped>
.three-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}
</style>
