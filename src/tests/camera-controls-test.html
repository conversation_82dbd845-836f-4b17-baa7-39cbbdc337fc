<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自由相机控制功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #4CAF50;
            margin-top: 0;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #4CAF50;
        }
        .status {
            font-weight: bold;
        }
        .status.pass {
            color: #4CAF50;
        }
        .status.fail {
            color: #f44336;
        }
        .status.pending {
            color: #ff9800;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .code-block {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 自由相机控制功能测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>请按照以下步骤测试自由相机的各项功能。每完成一项测试，请在相应的状态栏标记结果。</p>
            <p><strong>前提条件：</strong>确保开发服务器正在运行，并在浏览器中打开主应用。</p>
        </div>

        <div class="test-section">
            <h3>🔧 基础功能测试</h3>
            
            <div class="test-item">
                <strong>1. 启用自由相机</strong>
                <p>在右侧控制面板中勾选"启用自由相机"</p>
                <p>预期结果：相机控制面板展开，显示所有控制选项</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>2. 键盘移动控制</strong>
                <p>使用WASD键移动相机，QE键上下移动</p>
                <p>预期结果：相机按预期方向移动，位置数值实时更新</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>3. 键盘旋转控制</strong>
                <p>使用方向键旋转相机视角</p>
                <p>预期结果：相机视角按预期旋转，角度数值实时更新</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>4. 鼠标控制</strong>
                <p>确保"启用鼠标控制"已勾选，左键拖拽旋转视角</p>
                <p>预期结果：鼠标拖拽能够控制相机旋转</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 精确控制测试</h3>
            
            <div class="test-item">
                <strong>5. 精确位置设置</strong>
                <p>在位置输入框中输入：X=5, Y=8, Z=12，点击"应用位置"</p>
                <p>预期结果：相机移动到指定位置，显示的位置数值更新</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>6. 精确角度设置</strong>
                <p>在角度输入框中输入：俯仰=-30°, 偏航=45°，点击"应用朝向"</p>
                <p>预期结果：相机旋转到指定角度，显示的角度数值更新</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>7. 看向目标功能</strong>
                <p>在目标输入框中输入：X=0, Y=0, Z=0，点击"看向目标"</p>
                <p>预期结果：相机自动朝向原点方向</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>📍 预设位置测试</h3>
            
            <div class="test-item">
                <strong>8. 预设位置功能</strong>
                <p>依次点击所有预设位置按钮：默认、车辆视角、侧面视角、俯视、近距离</p>
                <p>预期结果：相机平滑移动到各个预设位置</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>⚙️ 配置选项测试</h3>
            
            <div class="test-item">
                <strong>9. 移动速度调节</strong>
                <p>调节移动速度滑块，测试键盘移动速度变化</p>
                <p>预期结果：移动速度按滑块设置变化</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>10. 鼠标灵敏度调节</strong>
                <p>调节鼠标灵敏度滑块，测试鼠标控制灵敏度变化</p>
                <p>预期结果：鼠标控制灵敏度按滑块设置变化</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>11. 平滑移动功能</strong>
                <p>启用"平滑移动"，然后使用预设位置或精确位置设置</p>
                <p>预期结果：相机移动有平滑过渡动画效果</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>12. FOV调节</strong>
                <p>调节FOV滑块，观察视野角度变化</p>
                <p>预期结果：视野角度按滑块设置变化</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔄 切换和重置测试</h3>
            
            <div class="test-item">
                <strong>13. 跳转到相机视角</strong>
                <p>移动主相机到不同位置，然后点击"跳转到相机视角"</p>
                <p>预期结果：主相机跳转到自由相机的位置和角度</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>14. 重置相机位置</strong>
                <p>移动自由相机到任意位置，然后点击"重置相机位置"</p>
                <p>预期结果：自由相机回到默认位置(10, 10, 10)</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>15. 相机可见性切换</strong>
                <p>点击"隐藏相机"/"显示相机"按钮</p>
                <p>预期结果：场景中的红色球体（相机标识）显示/隐藏</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>

            <div class="test-item">
                <strong>16. 禁用自由相机</strong>
                <p>取消勾选"启用自由相机"</p>
                <p>预期结果：自由相机控制被禁用，轨道控制器重新启用</p>
                <p>状态：<span class="status pending">待测试</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果总结</h3>
            <div class="code-block">
                通过测试数量：<span id="passCount">0</span> / 16<br>
                失败测试数量：<span id="failCount">0</span> / 16<br>
                待测试数量：<span id="pendingCount">16</span> / 16
            </div>
            <p><strong>总体评估：</strong><span id="overallStatus" class="status pending">待完成</span></p>
        </div>

        <div class="instructions">
            <h3>🐛 问题报告</h3>
            <p>如果发现任何问题，请记录以下信息：</p>
            <ul>
                <li>测试项目编号和名称</li>
                <li>具体的操作步骤</li>
                <li>预期结果 vs 实际结果</li>
                <li>浏览器控制台错误信息（如有）</li>
                <li>浏览器类型和版本</li>
            </ul>
        </div>
    </div>

    <script>
        // 简单的测试状态管理
        function updateTestStatus(testNumber, status) {
            const statusElements = document.querySelectorAll('.test-item .status');
            if (statusElements[testNumber - 1]) {
                statusElements[testNumber - 1].textContent = status;
                statusElements[testNumber - 1].className = `status ${status.toLowerCase()}`;
            }
            updateSummary();
        }

        function updateSummary() {
            const statusElements = document.querySelectorAll('.test-item .status');
            let passCount = 0, failCount = 0, pendingCount = 0;
            
            statusElements.forEach(element => {
                const status = element.textContent.toLowerCase();
                if (status.includes('通过') || status.includes('pass')) passCount++;
                else if (status.includes('失败') || status.includes('fail')) failCount++;
                else pendingCount++;
            });

            document.getElementById('passCount').textContent = passCount;
            document.getElementById('failCount').textContent = failCount;
            document.getElementById('pendingCount').textContent = pendingCount;

            const overallStatus = document.getElementById('overallStatus');
            if (pendingCount === 0) {
                if (failCount === 0) {
                    overallStatus.textContent = '全部通过';
                    overallStatus.className = 'status pass';
                } else {
                    overallStatus.textContent = '部分失败';
                    overallStatus.className = 'status fail';
                }
            } else {
                overallStatus.textContent = '待完成';
                overallStatus.className = 'status pending';
            }
        }

        // 添加点击事件来快速更新状态
        document.querySelectorAll('.test-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                const currentStatus = item.querySelector('.status').textContent;
                let newStatus;
                if (currentStatus.includes('待测试')) newStatus = '通过';
                else if (currentStatus.includes('通过')) newStatus = '失败';
                else newStatus = '待测试';
                
                updateTestStatus(index + 1, newStatus);
            });
        });
    </script>
</body>
</html>
