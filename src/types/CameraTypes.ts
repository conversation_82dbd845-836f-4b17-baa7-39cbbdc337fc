import * as THREE from 'three'

export interface CameraConfig {
  fov: number // 基础FOV，实际由verticalFOV控制
  horizontalAngleRange: number
  pitchAngleRange: number
  movementSpeed: number
  rotationSpeed: number
  near: number
  far: number
  // 新增配置选项
  enableMouseControl: boolean
  mouseSensitivity: number
  smoothMovement: boolean
  smoothRotation: boolean
  smoothFactor: number
  // H-FOV和V-FOV控制（必需）
  horizontalFOV: number    // 水平视角角度
  verticalFOV: number      // 垂直视角角度
  aspectRatio: number      // 计算出的长宽比
}

export interface CameraState {
  position: THREE.Vector3
  rotation: THREE.Euler
  isActive: boolean
  isVisible: boolean
}

export interface CameraControls {
  forward: boolean
  backward: boolean
  left: boolean
  right: boolean
  up: boolean
  down: boolean
  rotateLeft: boolean
  rotateRight: boolean
  rotateUp: boolean
  rotateDown: boolean
}

export interface CameraEventHandlers {
  onPositionChange?: (position: THREE.Vector3) => void
  onRotationChange?: (rotation: THREE.Euler) => void
  onConfigChange?: (config: CameraConfig) => void
  onActiveStateChange?: (isActive: boolean) => void
}

// 新增接口
export interface CameraPreset {
  name: string
  position: { x: number; y: number; z: number }
  rotation: { x: number; y: number; z: number }
  description?: string
}

export interface MouseControlState {
  isMouseDown: boolean
  lastMouseX: number
  lastMouseY: number
  isPointerLocked: boolean
}

export interface CameraMovementOptions {
  smooth: boolean
  duration?: number
  easing?: (t: number) => number
}

// FOV控制相关接口
export interface FOVSettings {
  horizontalFOV: number    // 水平视角角度
  verticalFOV: number      // 垂直视角角度
  aspectRatio: number      // 计算出的长宽比
}

export interface TargetSize {
  width: number           // 目标宽度 (cm)
  height: number          // 目标高度 (cm)
}

export interface DistanceCalculation {
  recommendedDistance: number  // 建议距离
  distanceByWidth: number     // 基于宽度的距离
  distanceByHeight: number    // 基于高度的距离
}

// 第三人称相机配置
export interface ThirdPersonCameraConfig {
  distance: number
  height: number
  angle: number
  followSpeed: number
  rotationSpeed: number
  smoothing: boolean
}

// 第三人称预设
export interface ThirdPersonPreset {
  name: string
  config: ThirdPersonCameraConfig
  description?: string
}

// 自由相机实例接口
export interface FreeCameraInstance {
  id: string
  name: string
  isActive: boolean
  isVisible: boolean
  position: { x: number; y: number; z: number }
  rotation: { x: number; y: number; z: number }
  fovSettings: FOVSettings
  viewportFrameEnabled: boolean
}

// 激光控制接口
export interface LaserControl {
  cameraId: string
  enabled: boolean
  selected: boolean
}

// 多相机管理器配置
export interface FreeCameraManagerConfig {
  maxCameras: number
  defaultFOV: FOVSettings
  defaultPosition: { x: number; y: number; z: number }
  defaultRotation: { x: number; y: number; z: number }
}
