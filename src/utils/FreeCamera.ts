import * as THREE from 'three'
import { CameraConfig, CameraState, CameraControls, CameraEventHandlers, MouseControlState, CameraMovementOptions, CameraPreset, FOVSettings } from '../types/CameraTypes'
import { ViewportFrame, ViewportFrameConfig } from './ViewportFrame'

export class FreeCamera {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private renderer: THREE.WebGLRenderer
  private config: CameraConfig
  private state: CameraState
  private controls: CameraControls
  private eventHandlers: CameraEventHandlers
  
  // Visual representation
  private cameraGroup: THREE.Group
  private sphereMesh: THREE.Mesh
  private directionArrow: THREE.ArrowHelper
  private isInitialized: boolean = false
  private sphereVisible: boolean = true
  private directionVisible: boolean = true

  // FOV控制
  private fovSettings: FOVSettings = {
    horizontalFOV: 60,
    verticalFOV: 30,
    aspectRatio: 2.0
  }

  // 屏幕适配设置
  private screenAdaptation = {
    enabled: false,
    mode: 'fit',
    priority: 'auto'
  }

  // 精确FOV模式设置
  private precisionMode = {
    enabled: true, // 默认启用精确FOV模式
    forceAspectRatio: true // 强制使用计算出的长宽比
  }

  // 视野框架系统
  private viewportFrame: ViewportFrame | null = null

  // 相机名称
  private cameraName: string = ''
  
  // Movement and rotation
  private moveVector: THREE.Vector3 = new THREE.Vector3()
  private rotationEuler: THREE.Euler = new THREE.Euler()

  // Keyboard event handlers
  private keyDownHandler: (event: KeyboardEvent) => void
  private keyUpHandler: (event: KeyboardEvent) => void

  // Mouse control
  private mouseControlState: MouseControlState = {
    isMouseDown: false,
    lastMouseX: 0,
    lastMouseY: 0,
    isPointerLocked: false
  }
  private mouseDownHandler: (event: MouseEvent) => void
  private mouseUpHandler: (event: MouseEvent) => void
  private mouseMoveHandler: (event: MouseEvent) => void
  private pointerLockChangeHandler: () => void

  // Smooth movement
  private targetPosition: THREE.Vector3 = new THREE.Vector3()
  private targetRotation: THREE.Euler = new THREE.Euler()
  private isMovingToTarget: boolean = false
  private isRotatingToTarget: boolean = false
  private movementStartTime: number = 0
  private movementDuration: number = 1000 // ms
  
  constructor(scene: THREE.Scene, renderer: THREE.WebGLRenderer, eventHandlers: CameraEventHandlers = {}) {
    this.scene = scene
    this.renderer = renderer
    this.eventHandlers = eventHandlers

    // Initialize camera with fixed FOV for viewport simulation
    this.camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 1000)

    // Default configuration - 相机使用固定FOV，视野通过ViewportFrame模拟
    this.config = {
      fov: 60, // 固定FOV，不再动态调整
      horizontalAngleRange: Math.PI * 2, // 360 degrees
      pitchAngleRange: Math.PI * 0.8, // 144 degrees
      movementSpeed: 5,
      rotationSpeed: 0.02,
      near: 1, // Match main camera near plane
      far: 1000,
      // 新增配置
      enableMouseControl: false, // 默认关闭鼠标控制
      mouseSensitivity: 0.002,
      smoothMovement: false,
      smoothRotation: false,
      smoothFactor: 0.1,
      // 视野模拟设置（不直接影响相机FOV）
      horizontalFOV: 60,
      verticalFOV: 30,
      aspectRatio: 2.0
    }
    
    // Initial state
    this.state = {
      position: new THREE.Vector3(10, 10, 10),
      rotation: new THREE.Euler(0, 0, 0),
      isActive: false,
      isVisible: true
    }
    
    // Initialize controls
    this.controls = {
      forward: false,
      backward: false,
      left: false,
      right: false,
      up: false,
      down: false,
      rotateLeft: false,
      rotateRight: false,
      rotateUp: false,
      rotateDown: false
    }
    
    this.initializeVisualRepresentation()
    this.setupKeyboardListeners()
    this.setupMouseListeners()
    this.updateCameraFromState()

    // 初始化视野框架
    this.viewportFrame = new ViewportFrame(this.scene, this.camera, this.renderer)
  }
  
  private initializeVisualRepresentation() {
    // Create group to hold all visual elements
    this.cameraGroup = new THREE.Group()
    
    // Create red sphere to represent camera
    const sphereGeometry = new THREE.SphereGeometry(0.5, 16, 16)
    const sphereMaterial = new THREE.MeshBasicMaterial({ 
      color: 0xff0000,
      transparent: true,
      opacity: 0.8
    })
    this.sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial)
    this.cameraGroup.add(this.sphereMesh)
    
    // Create direction arrow
    const direction = new THREE.Vector3(0, 0, -1) // Forward direction
    const origin = new THREE.Vector3(0, 0, 0)
    const length = 3
    const color = 0x00ff00
    
    this.directionArrow = new THREE.ArrowHelper(direction, origin, length, color, length * 0.3, length * 0.1)
    this.cameraGroup.add(this.directionArrow)

    // Add to scene
    this.scene.add(this.cameraGroup)
    this.updateVisualPosition()

    this.isInitialized = true
  }
  
  private setupKeyboardListeners() {
    this.keyDownHandler = (event: KeyboardEvent) => {
      if (!this.state.isActive) return

      switch (event.code) {
        case 'KeyW':
          this.controls.forward = true
          break
        case 'KeyS':
          this.controls.backward = true
          break
        case 'KeyA':
          this.controls.left = true
          break
        case 'KeyD':
          this.controls.right = true
          break
        case 'KeyQ':
          this.controls.up = true
          break
        case 'KeyE':
          this.controls.down = true
          break
        case 'ArrowLeft':
          this.controls.rotateLeft = true
          event.preventDefault()
          break
        case 'ArrowRight':
          this.controls.rotateRight = true
          event.preventDefault()
          break
        case 'ArrowUp':
          this.controls.rotateUp = true
          event.preventDefault()
          break
        case 'ArrowDown':
          this.controls.rotateDown = true
          event.preventDefault()
          break
      }
    }

    this.keyUpHandler = (event: KeyboardEvent) => {
      switch (event.code) {
        case 'KeyW':
          this.controls.forward = false
          break
        case 'KeyS':
          this.controls.backward = false
          break
        case 'KeyA':
          this.controls.left = false
          break
        case 'KeyD':
          this.controls.right = false
          break
        case 'KeyQ':
          this.controls.up = false
          break
        case 'KeyE':
          this.controls.down = false
          break
        case 'ArrowLeft':
          this.controls.rotateLeft = false
          break
        case 'ArrowRight':
          this.controls.rotateRight = false
          break
        case 'ArrowUp':
          this.controls.rotateUp = false
          break
        case 'ArrowDown':
          this.controls.rotateDown = false
          break
      }
    }

    window.addEventListener('keydown', this.keyDownHandler)
    window.addEventListener('keyup', this.keyUpHandler)
  }

  private setupMouseListeners() {
    this.mouseDownHandler = (event: MouseEvent) => {
      if (!this.state.isActive || !this.config.enableMouseControl) return

      if (event.button === 0) { // Left mouse button
        this.mouseControlState.isMouseDown = true
        this.mouseControlState.lastMouseX = event.clientX
        this.mouseControlState.lastMouseY = event.clientY

        // Request pointer lock for better mouse control
        if (document.pointerLockElement !== document.body) {
          document.body.requestPointerLock()
        }

        event.preventDefault()
      }
    }

    this.mouseUpHandler = (event: MouseEvent) => {
      if (event.button === 0) {
        this.mouseControlState.isMouseDown = false
      }
    }

    this.mouseMoveHandler = (event: MouseEvent) => {
      if (!this.state.isActive || !this.config.enableMouseControl) return

      if (this.mouseControlState.isMouseDown || this.mouseControlState.isPointerLocked) {
        let deltaX: number, deltaY: number

        if (this.mouseControlState.isPointerLocked) {
          deltaX = event.movementX
          deltaY = event.movementY
        } else {
          deltaX = event.clientX - this.mouseControlState.lastMouseX
          deltaY = event.clientY - this.mouseControlState.lastMouseY
          this.mouseControlState.lastMouseX = event.clientX
          this.mouseControlState.lastMouseY = event.clientY
        }

        // Apply mouse sensitivity
        deltaX *= this.config.mouseSensitivity
        deltaY *= this.config.mouseSensitivity

        // Update rotation
        this.state.rotation.y -= deltaX // Horizontal rotation (yaw)
        this.state.rotation.x -= deltaY // Vertical rotation (pitch)

        // Apply angle constraints
        const maxPitch = this.config.pitchAngleRange / 2
        this.state.rotation.x = Math.max(-maxPitch, Math.min(maxPitch, this.state.rotation.x))

        this.eventHandlers.onRotationChange?.(this.state.rotation)
        event.preventDefault()
      }
    }

    this.pointerLockChangeHandler = () => {
      this.mouseControlState.isPointerLocked = document.pointerLockElement === document.body
    }

    window.addEventListener('mousedown', this.mouseDownHandler)
    window.addEventListener('mouseup', this.mouseUpHandler)
    window.addEventListener('mousemove', this.mouseMoveHandler)
    document.addEventListener('pointerlockchange', this.pointerLockChangeHandler)
  }
  
  update(deltaTime: number = 0.016) {
    if (!this.state.isActive) return

    this.handleMovement(deltaTime)
    this.handleRotation(deltaTime)
    this.handleSmoothMovement(deltaTime)
    this.updateCameraFromState()
    this.updateVisualPosition()

    // 更新视野框架
    if (this.viewportFrame) {
      this.viewportFrame.update()
    }
  }

  private handleSmoothMovement(deltaTime: number) {
    // Handle smooth position movement
    if (this.isMovingToTarget) {
      const elapsed = performance.now() - this.movementStartTime
      const progress = Math.min(elapsed / this.movementDuration, 1)
      const easedProgress = this.easeInOutCubic(progress)

      this.state.position.lerpVectors(
        this.state.position,
        this.targetPosition,
        easedProgress
      )

      if (progress >= 1) {
        this.state.position.copy(this.targetPosition)
        this.isMovingToTarget = false
        this.eventHandlers.onPositionChange?.(this.state.position)
      }
    }

    // Handle smooth rotation movement
    if (this.isRotatingToTarget) {
      const elapsed = performance.now() - this.movementStartTime
      const progress = Math.min(elapsed / this.movementDuration, 1)
      const easedProgress = this.easeInOutCubic(progress)

      // Interpolate rotation
      const currentQuat = new THREE.Quaternion().setFromEuler(this.state.rotation)
      const targetQuat = new THREE.Quaternion().setFromEuler(this.targetRotation)
      currentQuat.slerp(targetQuat, easedProgress)
      this.state.rotation.setFromQuaternion(currentQuat)

      if (progress >= 1) {
        this.state.rotation.copy(this.targetRotation)
        this.isRotatingToTarget = false
        this.eventHandlers.onRotationChange?.(this.state.rotation)
      }
    }
  }

  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }
  
  private handleMovement(deltaTime: number) {
    this.moveVector.set(0, 0, 0)

    // Calculate movement based on camera's current rotation
    const forward = new THREE.Vector3(0, 0, -1).applyEuler(this.state.rotation)
    const right = new THREE.Vector3(1, 0, 0).applyEuler(this.state.rotation)
    const up = new THREE.Vector3(0, 1, 0) // World up direction (independent of camera rotation)

    if (this.controls.forward) {
      this.moveVector.add(forward)
    }
    if (this.controls.backward) {
      this.moveVector.sub(forward)
    }
    if (this.controls.left) {
      this.moveVector.sub(right)
    }
    if (this.controls.right) {
      this.moveVector.add(right)
    }
    if (this.controls.up) {
      this.moveVector.add(up)
    }
    if (this.controls.down) {
      this.moveVector.sub(up)
    }

    // Normalize and apply speed
    if (this.moveVector.length() > 0) {
      this.moveVector.normalize()
      this.moveVector.multiplyScalar(this.config.movementSpeed * deltaTime * 60) // 60 FPS normalization
      this.state.position.add(this.moveVector)

      this.eventHandlers.onPositionChange?.(this.state.position)
    }
  }
  
  private handleRotation(deltaTime: number) {
    let rotationChanged = false
    
    if (this.controls.rotateLeft) {
      this.state.rotation.y += this.config.rotationSpeed
      rotationChanged = true
    }
    if (this.controls.rotateRight) {
      this.state.rotation.y -= this.config.rotationSpeed
      rotationChanged = true
    }
    if (this.controls.rotateUp) {
      this.state.rotation.x += this.config.rotationSpeed
      rotationChanged = true
    }
    if (this.controls.rotateDown) {
      this.state.rotation.x -= this.config.rotationSpeed
      rotationChanged = true
    }
    
    // Apply angle constraints
    const maxPitch = this.config.pitchAngleRange / 2
    this.state.rotation.x = Math.max(-maxPitch, Math.min(maxPitch, this.state.rotation.x))
    
    if (rotationChanged) {
      this.eventHandlers.onRotationChange?.(this.state.rotation)
    }
  }
  
  private updateCameraFromState() {
    this.camera.position.copy(this.state.position)
    this.camera.rotation.copy(this.state.rotation)

    // 保持相机固定的FOV设置，不再根据fovSettings动态调整
    // 相机将使用默认的FOV设置，视野范围通过ViewportFrame模拟
    this.camera.near = this.config.near
    this.camera.far = this.config.far

    // 使用屏幕的实际长宽比，保持相机渲染的一致性
    const canvas = this.camera.userData?.canvas || document.querySelector('canvas')
    if (canvas) {
      this.camera.aspect = canvas.clientWidth / canvas.clientHeight
    } else {
      this.camera.aspect = window.innerWidth / window.innerHeight
    }

    this.camera.updateProjectionMatrix()
  }


  
  private updateVisualPosition() {
    if (!this.isInitialized) return
    
    this.cameraGroup.position.copy(this.state.position)
    this.cameraGroup.rotation.copy(this.state.rotation)
    this.cameraGroup.visible = this.state.isVisible
    
    // Update arrow direction
    const direction = new THREE.Vector3(0, 0, -1).applyEuler(this.state.rotation)
    this.directionArrow.setDirection(direction)
  }
  
  // Public methods
  setActive(active: boolean) {
    this.state.isActive = active
    this.eventHandlers.onActiveStateChange?.(active)
  }
  
  setVisible(visible: boolean) {
    this.state.isVisible = visible
    this.updateVisualPosition()
  }
  
  setPosition(x: number, y: number, z: number) {
    this.state.position.set(x, y, z)
    this.updateCameraFromState()
    this.updateVisualPosition()
    this.eventHandlers.onPositionChange?.(this.state.position)
  }
  
  setRotation(x: number, y: number, z: number) {
    this.state.rotation.set(x, y, z)
    this.updateCameraFromState()
    this.updateVisualPosition()
    this.eventHandlers.onRotationChange?.(this.state.rotation)
  }

  // 平滑移动到指定位置
  moveToPosition(x: number, y: number, z: number, options: CameraMovementOptions = { smooth: false }) {
    if (options.smooth) {
      this.targetPosition.set(x, y, z)
      this.isMovingToTarget = true
      this.movementStartTime = performance.now()
      this.movementDuration = options.duration || 1000
    } else {
      this.setPosition(x, y, z)
    }
  }

  // 平滑旋转到指定角度
  rotateToAngles(x: number, y: number, z: number, options: CameraMovementOptions = { smooth: false }) {
    if (options.smooth) {
      this.targetRotation.set(x, y, z)
      this.isRotatingToTarget = true
      this.movementStartTime = performance.now()
      this.movementDuration = options.duration || 1000
    } else {
      this.setRotation(x, y, z)
    }
  }

  // 看向指定点
  lookAt(target: THREE.Vector3, options: CameraMovementOptions = { smooth: false }) {
    const direction = target.clone().sub(this.state.position).normalize()
    const pitch = Math.asin(-direction.y)
    const yaw = Math.atan2(direction.x, direction.z)

    this.rotateToAngles(pitch, yaw, 0, options)
  }

  // 围绕目标点旋转
  orbitAroundTarget(target: THREE.Vector3, radius: number, azimuth: number, elevation: number, options: CameraMovementOptions = { smooth: false }) {
    const x = target.x + radius * Math.cos(elevation) * Math.sin(azimuth)
    const y = target.y + radius * Math.sin(elevation)
    const z = target.z + radius * Math.cos(elevation) * Math.cos(azimuth)

    this.moveToPosition(x, y, z, options)
    this.lookAt(target, options)
  }

  // 设置预设位置
  applyPreset(preset: CameraPreset, options: CameraMovementOptions = { smooth: true }) {
    this.moveToPosition(preset.position.x, preset.position.y, preset.position.z, options)
    this.rotateToAngles(preset.rotation.x, preset.rotation.y, preset.rotation.z, options)
  }

  // 获取当前朝向的方向向量
  getForwardDirection(): THREE.Vector3 {
    return new THREE.Vector3(0, 0, -1).applyEuler(this.state.rotation)
  }

  // 获取当前朝向的右方向向量
  getRightDirection(): THREE.Vector3 {
    return new THREE.Vector3(1, 0, 0).applyEuler(this.state.rotation)
  }

  // 获取当前朝向的上方向向量
  getUpDirection(): THREE.Vector3 {
    return new THREE.Vector3(0, 1, 0).applyEuler(this.state.rotation)
  }

  // 启用/禁用鼠标控制
  setMouseControlEnabled(enabled: boolean) {
    this.config.enableMouseControl = enabled
    if (!enabled && this.mouseControlState.isPointerLocked) {
      document.exitPointerLock()
    }
  }

  // 设置鼠标灵敏度
  setMouseSensitivity(sensitivity: number) {
    this.config.mouseSensitivity = Math.max(0.0001, Math.min(0.01, sensitivity))
  }

  // 控制相机球体显示
  setCameraSphereVisible(visible: boolean) {
    this.sphereVisible = visible
    if (this.sphereMesh) {
      this.sphereMesh.visible = visible
    }
  }

  // 控制方向指示线显示
  setDirectionIndicatorVisible(visible: boolean) {
    this.directionVisible = visible
    if (this.directionArrow) {
      this.directionArrow.visible = visible
    }
  }

  // 获取当前显示状态
  isCameraSphereVisible(): boolean {
    return this.sphereVisible
  }

  isDirectionIndicatorVisible(): boolean {
    return this.directionVisible
  }

  // 视野模拟方法 - 不再修改相机FOV，只更新视野框架模拟
  updateFOVSettings(horizontalFOV: number, verticalFOV: number, aspectRatio: number) {
    this.fovSettings.horizontalFOV = horizontalFOV
    this.fovSettings.verticalFOV = verticalFOV
    this.fovSettings.aspectRatio = aspectRatio

    // 不再修改相机的FOV和aspect参数，保持相机固定设置
    // 相机将保持默认的FOV设置，通过视野框架来模拟不同的视野范围

    // 更新视野框架以模拟指定的视野范围
    if (this.viewportFrame) {
      this.viewportFrame.setFOV(horizontalFOV, verticalFOV)
      // 确保视野框架启用
      this.viewportFrame.setEnabled(true)
    }

    // 触发配置更新事件，但不再包含相机FOV修改
    this.eventHandlers.onConfigChange?.({
      ...this.config,
      // 保持相机原有设置
      fov: this.camera.fov,
      horizontalFOV: horizontalFOV,
      verticalFOV: verticalFOV,
      aspectRatio: aspectRatio
    } as any)

    // 输出调试信息（开发时可用）
    if (process.env.NODE_ENV === 'development') {
      console.log('Viewport Simulation Updated:', {
        requested: { h: horizontalFOV, v: verticalFOV },
        camera: { fov: this.camera.fov.toFixed(2), aspect: this.camera.aspect.toFixed(3) },
        aspectRatio: aspectRatio.toFixed(3),
        mode: 'Viewport Frame Simulation'
      })
    }
  }

  // 获取当前FOV设置
  getFOVSettings(): FOVSettings {
    return { ...this.fovSettings }
  }

  // 根据目标尺寸和距离计算FOV
  calculateFOVFromTarget(targetWidth: number, targetHeight: number, distance: number): FOVSettings {
    // 计算水平和垂直FOV
    const horizontalFOV = 2 * Math.atan(targetWidth / (2 * distance)) * 180 / Math.PI
    const verticalFOV = 2 * Math.atan(targetHeight / (2 * distance)) * 180 / Math.PI
    const aspectRatio = targetWidth / targetHeight

    return {
      horizontalFOV,
      verticalFOV,
      aspectRatio
    }
  }

  // 根据FOV和目标尺寸计算建议距离
  calculateDistanceFromFOV(targetWidth: number, targetHeight: number): { byWidth: number, byHeight: number, recommended: number } {
    const horizontalRad = this.fovSettings.horizontalFOV * Math.PI / 180
    const verticalRad = this.fovSettings.verticalFOV * Math.PI / 180

    const distanceByWidth = (targetWidth / 2) / Math.tan(horizontalRad / 2)
    const distanceByHeight = (targetHeight / 2) / Math.tan(verticalRad / 2)

    // 建议距离取两者的平均值
    const recommended = (distanceByWidth + distanceByHeight) / 2

    return {
      byWidth: distanceByWidth,
      byHeight: distanceByHeight,
      recommended: recommended
    }
  }

  // 屏幕适配控制方法
  setScreenAdaptation(enabled: boolean, mode: string = 'fit', priority: string = 'auto') {
    this.screenAdaptation.enabled = enabled
    this.screenAdaptation.mode = mode
    this.screenAdaptation.priority = priority
    this.updateCameraFromState()
  }

  getScreenAdaptation() {
    return { ...this.screenAdaptation }
  }

  // 更新屏幕尺寸时调用
  updateScreenAspect() {
    if (this.screenAdaptation.enabled) {
      this.updateCameraFromState()
    }
  }
  
  updateConfig(newConfig: Partial<CameraConfig>) {
    this.config = { ...this.config, ...newConfig }

    // 如果配置包含H-FOV和V-FOV，同步更新fovSettings
    if (newConfig.horizontalFOV !== undefined) {
      this.fovSettings.horizontalFOV = newConfig.horizontalFOV
    }
    if (newConfig.verticalFOV !== undefined) {
      this.fovSettings.verticalFOV = newConfig.verticalFOV
    }
    if (newConfig.aspectRatio !== undefined) {
      this.fovSettings.aspectRatio = newConfig.aspectRatio
    }

    this.updateCameraFromState()
    this.eventHandlers.onConfigChange?.(this.config)
  }
  
  getCamera(): THREE.PerspectiveCamera {
    return this.camera
  }
  
  getConfig(): CameraConfig {
    return { ...this.config }
  }
  
  getState(): CameraState {
    return {
      position: this.state.position.clone(),
      rotation: this.state.rotation.clone(),
      isActive: this.state.isActive,
      isVisible: this.state.isVisible
    }
  }

  // 同步相机参数
  syncWithMainCamera(mainCamera: THREE.PerspectiveCamera) {
    // 只在没有自定义FOV设置时同步aspect ratio
    if (this.fovSettings.horizontalFOV === 60 && this.fovSettings.verticalFOV === 30) {
      this.camera.aspect = mainCamera.aspect
      this.camera.updateProjectionMatrix()
    }
  }

  // 重置FOV设置到默认值
  resetFOVToDefault() {
    this.fovSettings = {
      horizontalFOV: 60,
      verticalFOV: 30,
      aspectRatio: 2.0
    }

    // 同步更新config
    this.config.horizontalFOV = 60
    this.config.verticalFOV = 30
    this.config.aspectRatio = 2.0
    this.config.fov = 30 // 使用V-FOV

    // 应用到相机
    this.updateCameraFromState()
  }

  // 应用窗口aspect ratio（保持FOV设置）
  applyWindowAspectRatio(windowAspect: number) {
    // 如果用户没有自定义FOV，则使用窗口比例
    if (this.fovSettings.horizontalFOV === 60 && this.fovSettings.verticalFOV === 30) {
      this.camera.aspect = windowAspect
      this.camera.updateProjectionMatrix()
    }
    // 否则保持用户设置的FOV比例
  }

  // 设置精确FOV模式
  setPrecisionMode(enabled: boolean, forceAspectRatio: boolean = true) {
    this.precisionMode.enabled = enabled
    this.precisionMode.forceAspectRatio = forceAspectRatio

    // 立即应用新的模式设置
    this.updateCameraFromState()
  }

  // 获取精确FOV模式状态
  getPrecisionMode(): { enabled: boolean; forceAspectRatio: boolean } {
    return { ...this.precisionMode }
  }

  // 视野框架控制方法
  setViewportFrameEnabled(enabled: boolean) {
    if (this.viewportFrame) {
      this.viewportFrame.setEnabled(enabled)
    }
  }

  getViewportFrameEnabled(): boolean {
    return this.viewportFrame ? this.viewportFrame.getConfig().enabled : false
  }

  updateViewportFrameConfig(config: Partial<ViewportFrameConfig>) {
    if (this.viewportFrame) {
      this.viewportFrame.updateConfig(config)
    }
  }

  getViewportFrameConfig(): ViewportFrameConfig | null {
    return this.viewportFrame ? this.viewportFrame.getConfig() : null
  }

  // Enhanced viewport frame methods
  setViewportFrameScreenSpaceMode(enabled: boolean) {
    if (this.viewportFrame) {
      this.viewportFrame.setScreenSpaceMaskMode(enabled)
    }
  }

  setViewportFrameMaintainAspectRatio(enabled: boolean) {
    if (this.viewportFrame) {
      this.viewportFrame.setMaintainAspectRatio(enabled)
    }
  }

  setViewportFrameScaleToFit(enabled: boolean) {
    if (this.viewportFrame) {
      this.viewportFrame.setScaleToFit(enabled)
    }
  }

  getViewportFrameDimensions(): { width: number; height: number } {
    return this.viewportFrame ? this.viewportFrame.getViewportDimensions() : { width: 0, height: 0 }
  }

  // Handle window resize for viewport frame
  onWindowResize() {
    if (this.viewportFrame) {
      this.viewportFrame.onWindowResize()
    }
  }

  // Get viewport frame status
  getViewportFrameStatus() {
    return this.viewportFrame ? this.viewportFrame.getViewportStatus() : null
  }



  // 设置相机名称
  setName(name: string): void {
    this.cameraName = name
  }

  // 获取相机名称
  getName(): string {
    return this.cameraName
  }

  // 检查视野框架是否启用
  isViewportFrameEnabled(): boolean {
    return this.viewportFrame ? this.viewportFrame.getConfig().enabled : false
  }

  // 清理资源
  dispose(): void {
    // 移除事件监听器
    this.removeEventListeners()

    // 清理视觉表示
    if (this.cameraGroup) {
      this.scene.remove(this.cameraGroup)
    }

    // 清理视野框架
    if (this.viewportFrame) {
      this.viewportFrame.dispose()
      this.viewportFrame = null
    }
  }
}
