import * as THREE from 'three'
import { FreeCamera } from './FreeCamera'
import { FreeCameraInstance, FreeCameraManagerConfig, FOVSettings, CameraEventHandlers } from '../types/CameraTypes'

export class FreeCameraManager {
  private scene: THREE.Scene
  private renderer: THREE.WebGLRenderer
  private config: FreeCameraManagerConfig
  private cameras: Map<string, FreeCamera> = new Map()
  private activeCameraId: string | null = null
  private eventHandlers: CameraEventHandlers

  constructor(
    scene: THREE.Scene, 
    renderer: THREE.WebGLRenderer, 
    config: Partial<FreeCameraManagerConfig> = {},
    eventHandlers: CameraEventHandlers = {}
  ) {
    this.scene = scene
    this.renderer = renderer
    this.eventHandlers = eventHandlers
    
    this.config = {
      maxCameras: 10,
      defaultFOV: { horizontalFOV: 60, verticalFOV: 30, aspectRatio: 2.0 },
      defaultPosition: { x: 10, y: 10, z: 10 },
      defaultRotation: { x: 0, y: 0, z: 0 },
      ...config
    }
  }

  // 创建新的自由相机
  createFreeCamera(name?: string): string {
    if (this.cameras.size >= this.config.maxCameras) {
      throw new Error(`最多只能创建 ${this.config.maxCameras} 个自由相机`)
    }

    const id = this.generateCameraId()
    const cameraName = name || `激光${this.cameras.size + 1}`
    
    const camera = new FreeCamera(this.scene, this.renderer, {
      ...this.eventHandlers,
      onPositionChange: (position) => {
        this.eventHandlers.onPositionChange?.(position)
        // 触发相机位置更新事件
        this.dispatchCameraEvent('positionChange', { cameraId: id, position })
      },
      onRotationChange: (rotation) => {
        this.eventHandlers.onRotationChange?.(rotation)
        // 触发相机旋转更新事件
        this.dispatchCameraEvent('rotationChange', { cameraId: id, rotation })
      },
      onActiveStateChange: (isActive) => {
        if (isActive) {
          this.setActiveCamera(id)
        }
        this.eventHandlers.onActiveStateChange?.(isActive)
      }
    })

    // 设置默认位置和旋转
    camera.setPosition(
      this.config.defaultPosition.x,
      this.config.defaultPosition.y,
      this.config.defaultPosition.z
    )
    camera.setRotation(
      this.config.defaultRotation.x,
      this.config.defaultRotation.y,
      this.config.defaultRotation.z
    )

    // 设置默认FOV
    camera.updateFOVSettings(
      this.config.defaultFOV.horizontalFOV,
      this.config.defaultFOV.verticalFOV,
      this.config.defaultFOV.aspectRatio
    )

    // 设置相机名称
    camera.setName(cameraName)

    this.cameras.set(id, camera)
    
    // 触发相机创建事件
    this.dispatchCameraEvent('cameraCreated', { cameraId: id, name: cameraName })
    
    return id
  }

  // 删除自由相机
  deleteFreeCamera(id: string): boolean {
    const camera = this.cameras.get(id)
    if (!camera) return false

    // 如果是当前激活的相机，先取消激活
    if (this.activeCameraId === id) {
      camera.setActive(false)
      this.activeCameraId = null
    }

    // 清理相机资源
    camera.dispose()
    this.cameras.delete(id)
    
    // 触发相机删除事件
    this.dispatchCameraEvent('cameraDeleted', { cameraId: id })
    
    return true
  }

  // 获取相机实例
  getCamera(id: string): FreeCamera | undefined {
    return this.cameras.get(id)
  }

  // 获取所有相机列表
  getAllCameras(): FreeCameraInstance[] {
    const cameras: FreeCameraInstance[] = []
    
    this.cameras.forEach((camera, id) => {
      const state = camera.getState()
      const fovSettings = camera.getFOVSettings()
      
      cameras.push({
        id,
        name: camera.getName() || `激光${id}`,
        isActive: state.isActive,
        isVisible: state.isVisible,
        position: {
          x: state.position.x,
          y: state.position.y,
          z: state.position.z
        },
        rotation: {
          x: state.rotation.x,
          y: state.rotation.y,
          z: state.rotation.z
        },
        fovSettings,
        viewportFrameEnabled: camera.isViewportFrameEnabled()
      })
    })
    
    return cameras
  }

  // 设置激活相机
  setActiveCamera(id: string): boolean {
    const camera = this.cameras.get(id)
    if (!camera) return false

    // 取消之前激活的相机
    if (this.activeCameraId && this.activeCameraId !== id) {
      const prevCamera = this.cameras.get(this.activeCameraId)
      if (prevCamera) {
        prevCamera.setActive(false)
      }
    }

    // 激活新相机
    camera.setActive(true)
    this.activeCameraId = id
    
    // 触发激活状态变化事件
    this.dispatchCameraEvent('activeStateChange', { cameraId: id, isActive: true })
    
    return true
  }

  // 获取当前激活的相机ID
  getActiveCameraId(): string | null {
    return this.activeCameraId
  }

  // 获取当前激活的相机
  getActiveCamera(): FreeCamera | null {
    if (!this.activeCameraId) return null
    return this.cameras.get(this.activeCameraId) || null
  }

  // 切换相机可见性
  toggleCameraVisibility(id: string): boolean {
    const camera = this.cameras.get(id)
    if (!camera) return false

    const currentState = camera.getState()
    camera.setVisible(!currentState.isVisible)
    
    // 触发可见性变化事件
    this.dispatchCameraEvent('visibilityChange', { 
      cameraId: id, 
      isVisible: !currentState.isVisible 
    })
    
    return true
  }

  // 设置相机位置
  setCameraPosition(id: string, x: number, y: number, z: number, smooth: boolean = false): boolean {
    const camera = this.cameras.get(id)
    if (!camera) return false

    if (smooth) {
      camera.moveToPosition(x, y, z, { smooth: true, duration: 1000 })
    } else {
      camera.setPosition(x, y, z)
    }
    
    return true
  }

  // 设置相机旋转
  setCameraRotation(id: string, x: number, y: number, z: number, smooth: boolean = false): boolean {
    const camera = this.cameras.get(id)
    if (!camera) return false

    if (smooth) {
      camera.rotateToAngles(x, y, z, { smooth: true, duration: 1000 })
    } else {
      camera.setRotation(x, y, z)
    }
    
    return true
  }

  // 设置相机FOV
  setCameraFOV(id: string, horizontalFOV: number, verticalFOV: number, aspectRatio: number): boolean {
    const camera = this.cameras.get(id)
    if (!camera) return false

    camera.updateFOVSettings(horizontalFOV, verticalFOV, aspectRatio)
    return true
  }

  // 设置相机视野蒙板
  setCameraViewportFrame(id: string, enabled: boolean): boolean {
    const camera = this.cameras.get(id)
    if (!camera) return false

    camera.setViewportFrameEnabled(enabled)
    return true
  }

  // 重命名相机
  renameFreeCamera(id: string, newName: string): boolean {
    const camera = this.cameras.get(id)
    if (!camera) return false

    camera.setName(newName)
    
    // 触发重命名事件
    this.dispatchCameraEvent('cameraRenamed', { cameraId: id, name: newName })
    
    return true
  }

  // 更新所有相机
  update(): void {
    this.cameras.forEach(camera => {
      camera.update()
    })
  }

  // 清理所有相机
  dispose(): void {
    this.cameras.forEach(camera => {
      camera.dispose()
    })
    this.cameras.clear()
    this.activeCameraId = null
  }

  // 生成唯一相机ID
  private generateCameraId(): string {
    return `camera_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 触发相机事件
  private dispatchCameraEvent(eventType: string, data: any): void {
    if (this.renderer.domElement) {
      this.renderer.domElement.dispatchEvent(new CustomEvent(`freeCamera${eventType}`, {
        detail: data
      }))
    }
  }

  // 获取相机数量
  getCameraCount(): number {
    return this.cameras.size
  }

  // 获取最大相机数量
  getMaxCameras(): number {
    return this.config.maxCameras
  }
}
