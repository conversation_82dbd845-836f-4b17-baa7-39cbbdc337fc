import * as THREE from 'three'

export interface GlobalCameraConfig {
  movementSpeed: number
  smoothing: number
  minHeight: number
  maxHeight: number
  boundarySize: number
}

export interface GlobalCameraControls {
  moveForward: boolean
  moveBackward: boolean
  moveLeft: boolean
  moveRight: boolean
  moveUp: boolean
  moveDown: boolean
}

export class GlobalCamera {
  private camera: THREE.PerspectiveCamera
  private config: GlobalCameraConfig
  private controls: GlobalCameraControls
  private targetPosition: THREE.Vector3
  private currentVelocity: THREE.Vector3
  private isActive: boolean = false
  
  // Keyboard event handlers
  private keyDownHandler: (event: KeyboardEvent) => void
  private keyUpHandler: (event: KeyboardEvent) => void
  
  constructor(camera: THREE.PerspectiveCamera) {
    this.camera = camera
    
    // 默认配置
    this.config = {
      movementSpeed: 20,
      smoothing: 0.1,
      minHeight: 5,
      maxHeight: 200,
      boundarySize: 500
    }
    
    // 初始化控制状态
    this.controls = {
      moveForward: false,
      moveBackward: false,
      moveLeft: false,
      moveRight: false,
      moveUp: false,
      moveDown: false
    }
    
    this.targetPosition = camera.position.clone()
    this.currentVelocity = new THREE.Vector3()
    
    this.setupKeyboardListeners()
  }
  
  private setupKeyboardListeners() {
    this.keyDownHandler = (event: KeyboardEvent) => {
      if (!this.isActive) return
      
      switch (event.code) {
        case 'KeyW':
        case 'ArrowUp':
          this.controls.moveForward = true
          event.preventDefault()
          break
        case 'KeyS':
        case 'ArrowDown':
          this.controls.moveBackward = true
          event.preventDefault()
          break
        case 'KeyA':
        case 'ArrowLeft':
          this.controls.moveLeft = true
          event.preventDefault()
          break
        case 'KeyD':
        case 'ArrowRight':
          this.controls.moveRight = true
          event.preventDefault()
          break
        case 'KeyQ':
          this.controls.moveUp = true
          event.preventDefault()
          break
        case 'KeyE':
          this.controls.moveDown = true
          event.preventDefault()
          break
      }
    }
    
    this.keyUpHandler = (event: KeyboardEvent) => {
      switch (event.code) {
        case 'KeyW':
        case 'ArrowUp':
          this.controls.moveForward = false
          break
        case 'KeyS':
        case 'ArrowDown':
          this.controls.moveBackward = false
          break
        case 'KeyA':
        case 'ArrowLeft':
          this.controls.moveLeft = false
          break
        case 'KeyD':
        case 'ArrowRight':
          this.controls.moveRight = false
          break
        case 'KeyQ':
          this.controls.moveUp = false
          break
        case 'KeyE':
          this.controls.moveDown = false
          break
      }
    }
    
    window.addEventListener('keydown', this.keyDownHandler)
    window.addEventListener('keyup', this.keyUpHandler)
  }
  
  update(deltaTime: number) {
    if (!this.isActive) return
    
    // 计算移动方向（基于世界坐标系）
    const moveVector = new THREE.Vector3()
    
    if (this.controls.moveForward) {
      moveVector.z -= 1
    }
    if (this.controls.moveBackward) {
      moveVector.z += 1
    }
    if (this.controls.moveLeft) {
      moveVector.x -= 1
    }
    if (this.controls.moveRight) {
      moveVector.x += 1
    }
    if (this.controls.moveUp) {
      moveVector.y += 1
    }
    if (this.controls.moveDown) {
      moveVector.y -= 1
    }
    
    // 标准化移动向量并应用速度
    if (moveVector.length() > 0) {
      moveVector.normalize()
      moveVector.multiplyScalar(this.config.movementSpeed * deltaTime)
      this.targetPosition.add(moveVector)
    }
    
    // 应用边界限制
    this.applyBoundaries()
    
    // 平滑移动到目标位置
    this.camera.position.lerp(this.targetPosition, this.config.smoothing)
  }
  
  private applyBoundaries() {
    // 限制高度
    this.targetPosition.y = Math.max(
      this.config.minHeight,
      Math.min(this.config.maxHeight, this.targetPosition.y)
    )
    
    // 限制水平边界
    const boundary = this.config.boundarySize
    this.targetPosition.x = Math.max(-boundary, Math.min(boundary, this.targetPosition.x))
    this.targetPosition.z = Math.max(-boundary, Math.min(boundary, this.targetPosition.z))
  }
  
  // 设置全局视角（俯视角度）
  setGlobalPerspective(height: number = 50, angle: number = -Math.PI / 4) {
    this.targetPosition.y = height
    this.camera.position.copy(this.targetPosition)
    
    // 设置俯视角度
    this.camera.rotation.x = angle
    this.camera.rotation.y = 0
    this.camera.rotation.z = 0
  }
  
  // 重置到场景中心的全局视角
  resetToGlobalView() {
    this.targetPosition.set(0, 50, 0)
    this.setGlobalPerspective(50, -Math.PI / 4)
  }
  
  // 设置相机位置
  setPosition(x: number, y: number, z: number) {
    this.targetPosition.set(x, y, z)
    this.camera.position.copy(this.targetPosition)
  }
  
  // 获取当前位置
  getPosition(): THREE.Vector3 {
    return this.camera.position.clone()
  }
  
  // 激活/停用全局相机控制
  setActive(active: boolean) {
    this.isActive = active
    if (active) {
      // 激活时同步目标位置
      this.targetPosition.copy(this.camera.position)
    }
  }
  
  // 更新配置
  updateConfig(newConfig: Partial<GlobalCameraConfig>) {
    this.config = { ...this.config, ...newConfig }
  }
  
  // 获取配置
  getConfig(): GlobalCameraConfig {
    return { ...this.config }
  }
  
  // 检查是否激活
  isActiveCamera(): boolean {
    return this.isActive
  }
  
  // 清理资源
  dispose() {
    if (this.keyDownHandler) {
      window.removeEventListener('keydown', this.keyDownHandler)
    }
    if (this.keyUpHandler) {
      window.removeEventListener('keyup', this.keyUpHandler)
    }
  }
}
