import { VehicleController } from './VehicleController'

interface VehicleMessage {
  vehicleId: string
  action?: string
  x?: number
  y?: number
  z?: number
  direction?: string
  distance?: number
  angle?: number
  speed?: number
  // 履带控制参数
  trackNames?: string[]
  rotationSpeed?: number
  trackAction?: 'start' | 'stop' | 'setSpeed'
}

class MessageHandler {
  private vehicleController: VehicleController | null = null

  setVehicleController(controller: VehicleController) {
    this.vehicleController = controller
  }

  processMessage(message: VehicleMessage) {
    if (!this.vehicleController) {
      console.error('VehicleController not set')
      return
    }

    const {
      vehicleId, action, x, y, z, direction, distance, angle, speed = 2,
      trackNames, rotationSpeed, trackAction
    } = message

    if (!vehicleId) {
      console.error('Vehicle ID is required')
      return
    }

    console.log('Processing message:', message)

    try {
      switch (action) {
        case 'move':
          if (direction && distance !== undefined) {
            this.vehicleController.moveVehicleByDirection(vehicleId, direction, distance, speed)
          } else if (x !== undefined && z !== undefined) {
            const yPos = y !== undefined ? y : 0
            this.vehicleController.moveVehicleToPosition(vehicleId, x, yPos, z, speed)
          } else {
            console.error('Invalid move parameters')
          }
          break

        case 'turn':
        case 'rotate':
          if (direction && angle !== undefined) {
            this.vehicleController.rotateVehicle(vehicleId, direction, angle, speed)
          } else {
            console.error('Invalid rotation parameters')
          }
          break

        case 'reset':
          this.vehicleController.resetVehiclePosition(vehicleId)
          break

        case 'track':
        case 'trackControl':
          this.handleTrackControl(vehicleId, trackAction, trackNames, rotationSpeed)
          break

        default:
          // 如果没有指定action，尝试根据参数推断
          if (trackAction) {
            this.handleTrackControl(vehicleId, trackAction, trackNames, rotationSpeed)
          } else if (x !== undefined && z !== undefined) {
            const yPos = y !== undefined ? y : 0
            this.vehicleController.moveVehicleToPosition(vehicleId, x, yPos, z, speed)
          } else if (direction && distance !== undefined) {
            this.vehicleController.moveVehicleByDirection(vehicleId, direction, distance, speed)
          } else if (direction && angle !== undefined) {
            this.vehicleController.rotateVehicle(vehicleId, direction, angle, speed)
          } else {
            console.error('Unknown action or invalid parameters:', message)
          }
          break
      }
    } catch (error) {
      console.error('Error processing message:', error)
    }
  }

  // 处理履带控制
  private handleTrackControl(
    vehicleId: string,
    trackAction?: string,
    trackNames?: string[],
    rotationSpeed?: number
  ) {
    if (!this.vehicleController) return

    const defaultTrackNames = ['履带1', '履带2', '履带3']
    const tracks = trackNames || defaultTrackNames
    const speed = rotationSpeed || 1

    switch (trackAction) {
      case 'start':
        this.vehicleController.startTrackRotation(vehicleId, tracks, speed)
        break
      case 'stop':
        this.vehicleController.stopTrackRotation(vehicleId)
        break
      case 'setSpeed':
        this.vehicleController.setTrackRotationSpeed(vehicleId, speed)
        break
      default:
        console.error('Invalid track action:', trackAction)
        break
    }
  }

  // 预定义的电文模板
  getMessageTemplates() {
    return {
      moveToPosition: {
        vehicleId: 'car1',
        action: 'move',
        x: 10,
        y: 0,
        z: 5,
        speed: 2
      },
      moveForward: {
        vehicleId: 'car1',
        action: 'move',
        direction: 'forward',
        distance: 5,
        speed: 2
      },
      turnLeft: {
        vehicleId: 'car1',
        action: 'turn',
        direction: 'left',
        angle: 45,
        speed: 1
      },
      reset: {
        vehicleId: 'car1',
        action: 'reset'
      },
      // 履带控制模板
      startTracks: {
        vehicleId: 'car1',
        action: 'track',
        trackAction: 'start',
        trackNames: ['履带1', '履带2', '履带3'],
        rotationSpeed: 2
      },
      stopTracks: {
        vehicleId: 'car1',
        action: 'track',
        trackAction: 'stop'
      },
      setTrackSpeed: {
        vehicleId: 'car1',
        action: 'track',
        trackAction: 'setSpeed',
        rotationSpeed: 3
      }
    }
  }

  // 验证电文格式
  validateMessage(message: any): boolean {
    if (typeof message !== 'object' || message === null) {
      return false
    }

    if (!message.vehicleId || typeof message.vehicleId !== 'string') {
      return false
    }

    // 根据不同的action验证参数
    switch (message.action) {
      case 'move':
        if (message.direction && message.distance !== undefined) {
          return ['forward', 'backward', 'left', 'right'].includes(message.direction) &&
                 typeof message.distance === 'number'
        } else if (message.x !== undefined && message.z !== undefined) {
          return typeof message.x === 'number' && typeof message.z === 'number'
        }
        return false

      case 'turn':
      case 'rotate':
        return message.direction && message.angle !== undefined &&
               ['left', 'right'].includes(message.direction) &&
               typeof message.angle === 'number'

      case 'reset':
        return true

      default:
        // 如果没有action，检查是否有有效的参数组合
        return (message.x !== undefined && message.z !== undefined) ||
               (message.direction && message.distance !== undefined) ||
               (message.direction && message.angle !== undefined)
    }
  }

  // 获取车辆状态
  getVehicleStatus(vehicleId: string) {
    if (!this.vehicleController) {
      return null
    }

    return {
      position: this.vehicleController.getVehiclePosition(vehicleId),
      rotation: this.vehicleController.getVehicleRotation(vehicleId),
      isMoving: this.vehicleController.isVehicleMoving(vehicleId)
    }
  }

  // 获取所有车辆列表
  getAllVehicles() {
    if (!this.vehicleController) {
      return []
    }
    return this.vehicleController.getAllVehicles()
  }
}

// 创建单例实例
export const messageHandler = new MessageHandler()

// 在ThreeManager初始化后设置控制器的函数
export function initializeMessageHandler(vehicleController: VehicleController) {
  messageHandler.setVehicleController(vehicleController)
}
