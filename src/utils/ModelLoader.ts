import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

export class ModelLoader {
  private loader: GLTFLoader
  private loadingManager: THREE.LoadingManager

  constructor() {
    this.loadingManager = new THREE.LoadingManager()
    this.loader = new GLTFLoader(this.loadingManager)
    
    this.loadingManager.onLoad = () => {
      console.log('All models loaded')
    }
    
    this.loadingManager.onProgress = (url, loaded, total) => {
      console.log(`Loading progress: ${url} - ${loaded}/${total}`)
    }
    
    this.loadingManager.onError = (url) => {
      console.error(`Error loading: ${url}`)
    }
  }

  async loadGLBModel(url: string): Promise<THREE.Group> {
    return new Promise((resolve, reject) => {
      this.loader.load(
        url,
        (gltf) => {
          const model = gltf.scene
          
          // 设置模型属性
          model.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              child.castShadow = true
              child.receiveShadow = true
              
              // 确保材质正确
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach(mat => {
                    if (mat instanceof THREE.MeshStandardMaterial) {
                      mat.needsUpdate = true
                    }
                  })
                } else if (child.material instanceof THREE.MeshStandardMaterial) {
                  child.material.needsUpdate = true
                }
              }
            }
          })
          
          // 计算包围盒并居中
          const box = new THREE.Box3().setFromObject(model)
          const center = box.getCenter(new THREE.Vector3())
          model.position.sub(center)
          
          console.log('GLB model loaded successfully:', url)
          resolve(model)
        },
        (progress) => {
          console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%')
        },
        (error) => {
          console.error('Error loading GLB model:', error)
          reject(error)
        }
      )
    })
  }

  // 创建简单的车辆模型作为备用
  createFallbackVehicle(): THREE.Group {
    const vehicleGroup = new THREE.Group()

    // 车身
    const bodyGeometry = new THREE.BoxGeometry(4, 1.5, 2)
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x0066cc })
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial)
    body.position.y = 1
    body.castShadow = true
    vehicleGroup.add(body)

    // 车顶
    const roofGeometry = new THREE.BoxGeometry(2.5, 1, 1.8)
    const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x004499 })
    const roof = new THREE.Mesh(roofGeometry, roofMaterial)
    roof.position.set(0, 2, 0)
    roof.castShadow = true
    vehicleGroup.add(roof)

    // 车轮
    const wheelGeometry = new THREE.CylinderGeometry(0.5, 0.5, 0.3, 16)
    const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 })
    
    const wheelPositions = [
      [-1.5, 0.5, 1.2],
      [1.5, 0.5, 1.2],
      [-1.5, 0.5, -1.2],
      [1.5, 0.5, -1.2]
    ]

    wheelPositions.forEach(pos => {
      const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial)
      wheel.position.set(pos[0], pos[1], pos[2])
      wheel.rotation.z = Math.PI / 2
      wheel.castShadow = true
      vehicleGroup.add(wheel)
    })

    // 前灯
    const lightGeometry = new THREE.SphereGeometry(0.2, 8, 8)
    const lightMaterial = new THREE.MeshLambertMaterial({ 
      color: 0xffffaa,
      emissive: 0x444400
    })
    
    const leftLight = new THREE.Mesh(lightGeometry, lightMaterial)
    leftLight.position.set(-0.8, 1.2, 2.1)
    vehicleGroup.add(leftLight)
    
    const rightLight = new THREE.Mesh(lightGeometry, lightMaterial)
    rightLight.position.set(0.8, 1.2, 2.1)
    vehicleGroup.add(rightLight)

    return vehicleGroup
  }

  // 加载车辆模型（优先GLB，失败则使用备用）
  async loadVehicleModel(glbUrl?: string): Promise<THREE.Group> {
    if (glbUrl) {
      try {
        return await this.loadGLBModel(glbUrl)
      } catch (error) {
        console.warn('Failed to load GLB model, using fallback:', error)
      }
    }
    
    return this.createFallbackVehicle()
  }
}
