import * as THREE from 'three'
import { CameraConfig, CameraState, CameraEventHandlers, CameraMovementOptions } from '../types/CameraTypes'

export interface ThirdPersonCameraConfig {
  // 基础相机配置
  fov: number
  near: number
  far: number
  
  // 第三人称特有配置
  followDistance: number      // 跟随距离
  followHeight: number        // 跟随高度
  followAngle: number         // 水平跟随角度
  verticalAngle: number       // 垂直角度
  
  // 控制配置
  enableMouseControl: boolean
  mouseSensitivity: number
  smoothFollow: boolean
  smoothFactor: number
  
  // 移动配置
  moveSpeed: number
  rotateSpeed: number
  zoomSpeed: number
  
  // 限制
  minDistance: number
  maxDistance: number
  minVerticalAngle: number
  maxVerticalAngle: number
}

export interface ThirdPersonCameraState {
  target: THREE.Vector3       // 目标位置（通常是车辆位置）
  position: THREE.Vector3     // 相机位置
  rotation: THREE.Euler       // 相机旋转
  isActive: boolean
  isFollowing: boolean        // 是否跟随目标
}

export class ThirdPersonCamera {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private config: ThirdPersonCameraConfig
  private state: ThirdPersonCameraState
  private eventHandlers: CameraEventHandlers
  
  // 目标对象（如车辆）
  private targetObject: THREE.Object3D | null = null
  
  // 平滑移动
  private targetPosition: THREE.Vector3 = new THREE.Vector3()
  private targetLookAt: THREE.Vector3 = new THREE.Vector3()
  private isMovingToTarget: boolean = false
  private movementStartTime: number = 0
  private movementDuration: number = 1000
  
  // 鼠标控制
  private mouseState = {
    isDown: false,
    lastX: 0,
    lastY: 0,
    deltaX: 0,
    deltaY: 0
  }
  
  // 键盘控制
  private keyState = {
    forward: false,
    backward: false,
    left: false,
    right: false,
    up: false,
    down: false,
    rotateLeft: false,
    rotateRight: false,
    zoomIn: false,
    zoomOut: false
  }
  
  // 事件处理器
  private keyDownHandler: (event: KeyboardEvent) => void
  private keyUpHandler: (event: KeyboardEvent) => void
  private mouseDownHandler: (event: MouseEvent) => void
  private mouseUpHandler: (event: MouseEvent) => void
  private mouseMoveHandler: (event: MouseEvent) => void
  private wheelHandler: (event: WheelEvent) => void

  constructor(scene: THREE.Scene, eventHandlers: CameraEventHandlers = {}) {
    this.scene = scene
    this.eventHandlers = eventHandlers
    
    // 初始化相机
    this.camera = new THREE.PerspectiveCamera()
    
    // 默认配置
    this.config = {
      fov: 50,
      near: 1,
      far: 1000,
      
      followDistance: 15,
      followHeight: 8,
      followAngle: 0,
      verticalAngle: -0.3,
      
      enableMouseControl: true,
      mouseSensitivity: 0.003,
      smoothFollow: true,
      smoothFactor: 0.1,
      
      moveSpeed: 10,
      rotateSpeed: 1,
      zoomSpeed: 2,
      
      minDistance: 3,
      maxDistance: 50,
      minVerticalAngle: -Math.PI / 2,
      maxVerticalAngle: Math.PI / 3
    }
    
    // 初始状态
    this.state = {
      target: new THREE.Vector3(0, 0, 0),
      position: new THREE.Vector3(0, 8, 15),
      rotation: new THREE.Euler(0, 0, 0),
      isActive: false,
      isFollowing: true
    }
    
    this.setupEventListeners()
    this.updateCameraFromState()
  }
  
  private setupEventListeners() {
    // 键盘事件
    this.keyDownHandler = (event: KeyboardEvent) => {
      if (!this.state.isActive) return
      
      switch (event.code) {
        case 'KeyW': this.keyState.forward = true; break
        case 'KeyS': this.keyState.backward = true; break
        case 'KeyA': this.keyState.left = true; break
        case 'KeyD': this.keyState.right = true; break
        case 'KeyQ': this.keyState.up = true; break
        case 'KeyE': this.keyState.down = true; break
        case 'ArrowLeft': this.keyState.rotateLeft = true; break
        case 'ArrowRight': this.keyState.rotateRight = true; break
        case 'Equal': case 'NumpadAdd': this.keyState.zoomIn = true; break
        case 'Minus': case 'NumpadSubtract': this.keyState.zoomOut = true; break
      }
      event.preventDefault()
    }
    
    this.keyUpHandler = (event: KeyboardEvent) => {
      switch (event.code) {
        case 'KeyW': this.keyState.forward = false; break
        case 'KeyS': this.keyState.backward = false; break
        case 'KeyA': this.keyState.left = false; break
        case 'KeyD': this.keyState.right = false; break
        case 'KeyQ': this.keyState.up = false; break
        case 'KeyE': this.keyState.down = false; break
        case 'ArrowLeft': this.keyState.rotateLeft = false; break
        case 'ArrowRight': this.keyState.rotateRight = false; break
        case 'Equal': case 'NumpadAdd': this.keyState.zoomIn = false; break
        case 'Minus': case 'NumpadSubtract': this.keyState.zoomOut = false; break
      }
    }
    
    // 鼠标事件
    this.mouseDownHandler = (event: MouseEvent) => {
      if (!this.state.isActive || !this.config.enableMouseControl) return
      if (event.button === 0) { // 左键
        this.mouseState.isDown = true
        this.mouseState.lastX = event.clientX
        this.mouseState.lastY = event.clientY
        event.preventDefault()
      }
    }
    
    this.mouseUpHandler = (event: MouseEvent) => {
      if (event.button === 0) {
        this.mouseState.isDown = false
      }
    }
    
    this.mouseMoveHandler = (event: MouseEvent) => {
      if (!this.state.isActive || !this.config.enableMouseControl || !this.mouseState.isDown) return
      
      this.mouseState.deltaX = event.clientX - this.mouseState.lastX
      this.mouseState.deltaY = event.clientY - this.mouseState.lastY
      this.mouseState.lastX = event.clientX
      this.mouseState.lastY = event.clientY
      
      // 应用鼠标旋转
      this.config.followAngle -= this.mouseState.deltaX * this.config.mouseSensitivity
      this.config.verticalAngle -= this.mouseState.deltaY * this.config.mouseSensitivity
      
      // 限制垂直角度
      this.config.verticalAngle = Math.max(
        this.config.minVerticalAngle,
        Math.min(this.config.maxVerticalAngle, this.config.verticalAngle)
      )
      
      event.preventDefault()
    }
    
    // 滚轮事件
    this.wheelHandler = (event: WheelEvent) => {
      if (!this.state.isActive) return
      
      const delta = event.deltaY > 0 ? 1 : -1
      this.config.followDistance += delta * this.config.zoomSpeed
      this.config.followDistance = Math.max(
        this.config.minDistance,
        Math.min(this.config.maxDistance, this.config.followDistance)
      )
      
      event.preventDefault()
    }
    
    // 添加事件监听器
    window.addEventListener('keydown', this.keyDownHandler)
    window.addEventListener('keyup', this.keyUpHandler)
    window.addEventListener('mousedown', this.mouseDownHandler)
    window.addEventListener('mouseup', this.mouseUpHandler)
    window.addEventListener('mousemove', this.mouseMoveHandler)
    window.addEventListener('wheel', this.wheelHandler)
  }
  
  update(deltaTime: number = 0.016) {
    if (!this.state.isActive) return
    
    this.handleKeyboardInput(deltaTime)
    this.updateCameraPosition()
    this.handleSmoothMovement(deltaTime)
    this.updateCameraFromState()
    
    // 触发位置变化事件
    this.eventHandlers.onPositionChange?.(this.state.position)
    this.eventHandlers.onRotationChange?.(this.state.rotation)
  }
  
  private handleKeyboardInput(deltaTime: number) {
    const moveSpeed = this.config.moveSpeed * deltaTime
    const rotateSpeed = this.config.rotateSpeed * deltaTime
    
    // 目标移动（相对于世界坐标）
    if (this.keyState.forward) this.state.target.z -= moveSpeed
    if (this.keyState.backward) this.state.target.z += moveSpeed
    if (this.keyState.left) this.state.target.x -= moveSpeed
    if (this.keyState.right) this.state.target.x += moveSpeed
    if (this.keyState.up) this.state.target.y += moveSpeed
    if (this.keyState.down) this.state.target.y -= moveSpeed
    
    // 相机旋转
    if (this.keyState.rotateLeft) this.config.followAngle += rotateSpeed
    if (this.keyState.rotateRight) this.config.followAngle -= rotateSpeed
    
    // 缩放
    if (this.keyState.zoomIn) {
      this.config.followDistance = Math.max(this.config.minDistance, this.config.followDistance - moveSpeed)
    }
    if (this.keyState.zoomOut) {
      this.config.followDistance = Math.min(this.config.maxDistance, this.config.followDistance + moveSpeed)
    }
  }
  
  private updateCameraPosition() {
    if (this.state.isFollowing && this.targetObject) {
      // 如果有目标对象，更新目标位置
      this.state.target.copy(this.targetObject.position)
    }
    
    // 计算相机位置
    const offsetX = Math.sin(this.config.followAngle) * this.config.followDistance
    const offsetZ = Math.cos(this.config.followAngle) * this.config.followDistance
    const offsetY = this.config.followHeight + Math.sin(this.config.verticalAngle) * this.config.followDistance
    
    const targetPosition = new THREE.Vector3(
      this.state.target.x + offsetX,
      this.state.target.y + offsetY,
      this.state.target.z + offsetZ
    )
    
    if (this.config.smoothFollow) {
      this.state.position.lerp(targetPosition, this.config.smoothFactor)
    } else {
      this.state.position.copy(targetPosition)
    }
  }
  
  private handleSmoothMovement(deltaTime: number) {
    if (this.isMovingToTarget) {
      const elapsed = performance.now() - this.movementStartTime
      const progress = Math.min(elapsed / this.movementDuration, 1)
      const easedProgress = this.easeInOutCubic(progress)
      
      this.state.position.lerpVectors(this.state.position, this.targetPosition, easedProgress)
      
      if (progress >= 1) {
        this.state.position.copy(this.targetPosition)
        this.isMovingToTarget = false
      }
    }
  }
  
  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }
  
  private updateCameraFromState() {
    this.camera.position.copy(this.state.position)
    this.camera.lookAt(this.state.target)
    this.camera.fov = this.config.fov
    this.camera.near = this.config.near
    this.camera.far = this.config.far
    this.camera.updateProjectionMatrix()
    
    // 更新旋转状态
    this.state.rotation.copy(this.camera.rotation)
  }
  
  // 公共方法
  setActive(active: boolean) {
    this.state.isActive = active
    this.eventHandlers.onActiveStateChange?.(active)
  }
  
  setTarget(target: THREE.Vector3 | THREE.Object3D) {
    if (target instanceof THREE.Object3D) {
      this.targetObject = target
      this.state.target.copy(target.position)
    } else {
      this.targetObject = null
      this.state.target.copy(target)
    }
  }
  
  setFollowing(following: boolean) {
    this.state.isFollowing = following
  }
  
  // 设置相机参数
  setDistance(distance: number) {
    this.config.followDistance = Math.max(this.config.minDistance, Math.min(this.config.maxDistance, distance))
  }
  
  setHeight(height: number) {
    this.config.followHeight = height
  }
  
  setAngle(horizontal: number, vertical: number) {
    this.config.followAngle = horizontal
    this.config.verticalAngle = Math.max(this.config.minVerticalAngle, Math.min(this.config.maxVerticalAngle, vertical))
  }
  
  // 平滑移动到指定位置
  moveToPosition(position: THREE.Vector3, options: CameraMovementOptions = { smooth: true }) {
    if (options.smooth) {
      this.targetPosition.copy(position)
      this.isMovingToTarget = true
      this.movementStartTime = performance.now()
      this.movementDuration = options.duration || 1000
    } else {
      this.state.position.copy(position)
    }
  }
  
  // 获取器
  getCamera(): THREE.PerspectiveCamera {
    return this.camera
  }
  
  getConfig(): ThirdPersonCameraConfig {
    return { ...this.config }
  }
  
  getState(): ThirdPersonCameraState {
    return {
      target: this.state.target.clone(),
      position: this.state.position.clone(),
      rotation: this.state.rotation.clone(),
      isActive: this.state.isActive,
      isFollowing: this.state.isFollowing
    }
  }
  
  updateConfig(newConfig: Partial<ThirdPersonCameraConfig>) {
    this.config = { ...this.config, ...newConfig }
    this.updateCameraFromState()
    this.eventHandlers.onConfigChange?.(this.config as any)
  }
  
  // 同步相机参数
  syncWithMainCamera(mainCamera: THREE.PerspectiveCamera) {
    this.camera.aspect = mainCamera.aspect
    this.camera.updateProjectionMatrix()
  }
  
  dispose() {
    // 移除事件监听器
    window.removeEventListener('keydown', this.keyDownHandler)
    window.removeEventListener('keyup', this.keyUpHandler)
    window.removeEventListener('mousedown', this.mouseDownHandler)
    window.removeEventListener('mouseup', this.mouseUpHandler)
    window.removeEventListener('mousemove', this.mouseMoveHandler)
    window.removeEventListener('wheel', this.wheelHandler)
  }
}
