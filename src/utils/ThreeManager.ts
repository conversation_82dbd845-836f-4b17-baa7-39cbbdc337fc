import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { VehicleController } from './VehicleController'
import { initializeMessageHandler } from './MessageHandler'
import { ModelLoader } from './ModelLoader'
import { FreeCamera } from './FreeCamera'
import { FreeCameraManager } from './FreeCameraManager'
import { GlobalCamera, GlobalCameraConfig } from './GlobalCamera'
import { CameraConfig, CameraPreset, CameraMovementOptions, FOVSettings, FreeCameraInstance, LaserControl } from '../types/CameraTypes'

export class ThreeManager {
  private container: HTMLElement
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private renderer: THREE.WebGLRenderer
  private controls: OrbitControls
  private vehicleController: VehicleController
  private modelLoader: ModelLoader
  private animationId: number = 0

  // Free camera system
  private freeCamera: FreeCamera
  private freeCameraManager: FreeCameraManager
  private isFreeCameraMode: boolean = false
  private originalCameraPosition: THREE.Vector3 = new THREE.Vector3()
  private originalCameraRotation: THREE.Euler = new THREE.Euler()
  private lastFrameTime: number = 0

  // Global camera system
  private globalCamera: GlobalCamera
  private isGlobalCameraMode: boolean = false

  // Remote free camera control (在轨道相机模式下控制自由相机)
  private isRemoteFreeCameraControlEnabled: boolean = false
  private selectedLasers: string[] = [] // 选中的激光ID列表
  private controlMode: string = 'single' // 控制模式：single, multiple, sequential
  private remoteControlKeyState = {
    // Free camera position control (使用WASD + QE)
    freeCamForward: false,    // W键
    freeCamBackward: false,   // S键
    freeCamLeft: false,       // A键
    freeCamRight: false,      // D键
    freeCamUp: false,         // Q键
    freeCamDown: false,       // E键
    // Free camera rotation control (使用方向键)
    freeCamRotateLeft: false,  // 左方向键
    freeCamRotateRight: false, // 右方向键
    freeCamRotateUp: false,    // 上方向键
    freeCamRotateDown: false   // 下方向键
  }
  private remoteControlKeyDownHandler: (event: KeyboardEvent) => void
  private remoteControlKeyUpHandler: (event: KeyboardEvent) => void

  constructor(container: HTMLElement) {
    this.container = container
    this.scene = new THREE.Scene()
    this.camera = new THREE.PerspectiveCamera()
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,  // 启用抗锯齿
      alpha: true,      // 启用透明度
      powerPreference: "high-performance"  // 优先使用高性能GPU
    })
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.vehicleController = new VehicleController(this.scene)
    this.modelLoader = new ModelLoader()

    // Initialize free camera with event handlers
    this.freeCamera = new FreeCamera(this.scene, this.renderer, {
      onPositionChange: (position) => {
        // Trigger custom event for UI updates
        this.container.dispatchEvent(new CustomEvent('cameraPositionChange', {
          detail: { x: position.x, y: position.y, z: position.z }
        }))
      },
      onRotationChange: (rotation) => {
        // Trigger custom event for UI updates
        this.container.dispatchEvent(new CustomEvent('cameraRotationChange', {
          detail: { x: rotation.x, y: rotation.y, z: rotation.z }
        }))
      },
      onActiveStateChange: (isActive) => {
        this.isFreeCameraMode = isActive
        if (isActive) {
          this.enableFreeCameraMode()
        } else {
          this.disableFreeCameraMode()
        }
      }
    })

    // Initialize global camera
    this.globalCamera = new GlobalCamera(this.camera)

    // Initialize free camera manager
    this.freeCameraManager = new FreeCameraManager(this.scene, this.renderer, {
      maxCameras: 10,
      defaultFOV: { horizontalFOV: 60, verticalFOV: 30, aspectRatio: 2.0 },
      defaultPosition: { x: 10, y: 10, z: 10 },
      defaultRotation: { x: 0, y: 0, z: 0 }
    }, {
      onPositionChange: (position) => {
        this.container.dispatchEvent(new CustomEvent('cameraPositionChange', {
          detail: { x: position.x, y: position.y, z: position.z }
        }))
      },
      onRotationChange: (rotation) => {
        this.container.dispatchEvent(new CustomEvent('cameraRotationChange', {
          detail: { x: rotation.x, y: rotation.y, z: rotation.z }
        }))
      }
    })

    // Initialize remote control for free camera
    this.setupRemoteFreeCameraControl()
  }

  init() {
    this.setupScene()
    this.setupCamera()
    this.setupRenderer()
    this.setupLights()
    this.setupControls()
    this.setupGround()
    this.loadDefaultVehicle()
    this.animate()
    this.handleResize()

    // 初始化消息处理器
    initializeMessageHandler(this.vehicleController)
  }

  private setupRemoteFreeCameraControl() {
    this.remoteControlKeyDownHandler = (event: KeyboardEvent) => {
      if (!this.isRemoteFreeCameraControlEnabled) return

      switch (event.code) {
        // 自由相机位置控制 (WASD + QE)
        case 'KeyW':
          this.remoteControlKeyState.freeCamForward = true
          console.log('按下W键 - 前进')
          break
        case 'KeyS':
          this.remoteControlKeyState.freeCamBackward = true
          console.log('按下S键 - 后退')
          break
        case 'KeyA':
          this.remoteControlKeyState.freeCamLeft = true
          console.log('按下A键 - 左移')
          break
        case 'KeyD':
          this.remoteControlKeyState.freeCamRight = true
          console.log('按下D键 - 右移')
          break
        case 'KeyQ':
          this.remoteControlKeyState.freeCamUp = true
          console.log('按下Q键 - 上升')
          break
        case 'KeyE':
          this.remoteControlKeyState.freeCamDown = true
          console.log('按下E键 - 下降')
          break
        // 自由相机旋转控制 (方向键)
        case 'ArrowLeft':
          this.remoteControlKeyState.freeCamRotateLeft = true
          console.log('按下左方向键 - 左转')
          break
        case 'ArrowRight':
          this.remoteControlKeyState.freeCamRotateRight = true
          console.log('按下右方向键 - 右转')
          break
        case 'ArrowUp':
          this.remoteControlKeyState.freeCamRotateUp = true
          console.log('按下上方向键 - 上仰')
          break
        case 'ArrowDown':
          this.remoteControlKeyState.freeCamRotateDown = true
          console.log('按下下方向键 - 下俯')
          break
      }
      event.preventDefault()
    }

    this.remoteControlKeyUpHandler = (event: KeyboardEvent) => {
      switch (event.code) {
        // 自由相机位置控制
        case 'KeyW': this.remoteControlKeyState.freeCamForward = false; break
        case 'KeyS': this.remoteControlKeyState.freeCamBackward = false; break
        case 'KeyA': this.remoteControlKeyState.freeCamLeft = false; break
        case 'KeyD': this.remoteControlKeyState.freeCamRight = false; break
        case 'KeyQ': this.remoteControlKeyState.freeCamUp = false; break
        case 'KeyE': this.remoteControlKeyState.freeCamDown = false; break
        // 自由相机旋转控制
        case 'ArrowLeft': this.remoteControlKeyState.freeCamRotateLeft = false; break
        case 'ArrowRight': this.remoteControlKeyState.freeCamRotateRight = false; break
        case 'ArrowUp': this.remoteControlKeyState.freeCamRotateUp = false; break
        case 'ArrowDown': this.remoteControlKeyState.freeCamRotateDown = false; break
      }
    }

    window.addEventListener('keydown', this.remoteControlKeyDownHandler)
    window.addEventListener('keyup', this.remoteControlKeyUpHandler)
  }

  private updateRemoteFreeCameraControl(deltaTime: number) {
    if (!this.isRemoteFreeCameraControlEnabled) return

    const moveSpeed = 10 * deltaTime // 移动速度
    const rotateSpeed = 1 * deltaTime // 旋转速度

    // 根据控制模式决定要控制的激光
    let targetCameras: string[] = []

    if (this.controlMode === 'single') {
      // 单激光模式：控制当前激活的激光
      const activeCameraId = this.freeCameraManager.getActiveCameraId()
      if (activeCameraId) {
        targetCameras = [activeCameraId]
      }
    } else if (this.controlMode === 'multiple') {
      // 多激光模式：控制所有选中的激光
      targetCameras = this.selectedLasers
    } else if (this.controlMode === 'sequential') {
      // 轮换模式：暂时使用选中的第一个激光
      if (this.selectedLasers.length > 0) {
        targetCameras = [this.selectedLasers[0]]
      }
    }

    // 调试信息
    if (targetCameras.length > 0) {
      console.log(`控制模式: ${this.controlMode}, 目标激光: ${targetCameras.join(', ')}, 选中激光: ${this.selectedLasers.join(', ')}`)
    }

    // 如果没有目标激光，回退到原来的单一自由相机控制
    if (targetCameras.length === 0) {
      this.updateSingleFreeCameraControl(deltaTime)
      return
    }

    // 控制选中的激光
    targetCameras.forEach(cameraId => {
      this.updateSingleLaserControl(cameraId, deltaTime, moveSpeed, rotateSpeed)
    })
  }

  // 更新单个激光的控制
  private updateSingleLaserControl(cameraId: string, deltaTime: number, moveSpeed: number, rotateSpeed: number) {
    const camera = this.freeCameraManager.getCamera(cameraId)
    if (!camera) return

    // 获取激光当前状态
    const cameraState = camera.getState()
    const currentPosition = cameraState.position.clone()
    const currentRotation = cameraState.rotation.clone()

    // 计算移动方向（基于激光当前朝向）
    const forward = new THREE.Vector3(0, 0, -1).applyEuler(currentRotation)
    const right = new THREE.Vector3(1, 0, 0).applyEuler(currentRotation)
    const up = new THREE.Vector3(0, 1, 0) // 世界上方向

    let positionChanged = false
    let rotationChanged = false

    // 处理位置移动
    if (this.remoteControlKeyState.freeCamForward) {
      currentPosition.add(forward.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamBackward) {
      currentPosition.sub(forward.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamLeft) {
      currentPosition.sub(right.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamRight) {
      currentPosition.add(right.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamUp) {
      currentPosition.add(up.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamDown) {
      currentPosition.sub(up.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }

    // 处理旋转
    if (this.remoteControlKeyState.freeCamRotateLeft) {
      currentRotation.y += rotateSpeed
      rotationChanged = true
    }
    if (this.remoteControlKeyState.freeCamRotateRight) {
      currentRotation.y -= rotateSpeed
      rotationChanged = true
    }
    if (this.remoteControlKeyState.freeCamRotateUp) {
      currentRotation.x += rotateSpeed
      rotationChanged = true
    }
    if (this.remoteControlKeyState.freeCamRotateDown) {
      currentRotation.x -= rotateSpeed
      rotationChanged = true
    }

    // 限制俯仰角度
    const maxPitch = Math.PI / 2 * 0.8 // 限制在±72度
    currentRotation.x = Math.max(-maxPitch, Math.min(maxPitch, currentRotation.x))

    // 应用变化到指定的激光
    if (positionChanged) {
      camera.setPosition(currentPosition.x, currentPosition.y, currentPosition.z)
    }
    if (rotationChanged) {
      camera.setRotation(currentRotation.x, currentRotation.y, currentRotation.z)
    }

    // 触发远程控制状态更新事件
    if (positionChanged || rotationChanged) {
      this.container.dispatchEvent(new CustomEvent('remoteFreeCameraUpdate', {
        detail: {
          cameraId,
          position: { x: currentPosition.x, y: currentPosition.y, z: currentPosition.z },
          rotation: { x: currentRotation.x, y: currentRotation.y, z: currentRotation.z }
        }
      }))
    }
  }

  // 更新单一自由相机控制（向后兼容）
  private updateSingleFreeCameraControl(deltaTime: number) {
    const moveSpeed = 10 * deltaTime
    const rotateSpeed = 1 * deltaTime

    const freeCameraState = this.freeCamera.getState()
    const currentPosition = freeCameraState.position.clone()
    const currentRotation = freeCameraState.rotation.clone()

    const forward = new THREE.Vector3(0, 0, -1).applyEuler(currentRotation)
    const right = new THREE.Vector3(1, 0, 0).applyEuler(currentRotation)
    const up = new THREE.Vector3(0, 1, 0)

    let positionChanged = false
    let rotationChanged = false

    // 处理位置移动
    if (this.remoteControlKeyState.freeCamForward) {
      currentPosition.add(forward.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamBackward) {
      currentPosition.sub(forward.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamLeft) {
      currentPosition.sub(right.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamRight) {
      currentPosition.add(right.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamUp) {
      currentPosition.add(up.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }
    if (this.remoteControlKeyState.freeCamDown) {
      currentPosition.sub(up.clone().multiplyScalar(moveSpeed))
      positionChanged = true
    }

    // 处理旋转
    if (this.remoteControlKeyState.freeCamRotateLeft) {
      currentRotation.y += rotateSpeed
      rotationChanged = true
    }
    if (this.remoteControlKeyState.freeCamRotateRight) {
      currentRotation.y -= rotateSpeed
      rotationChanged = true
    }
    if (this.remoteControlKeyState.freeCamRotateUp) {
      currentRotation.x += rotateSpeed
      rotationChanged = true
    }
    if (this.remoteControlKeyState.freeCamRotateDown) {
      currentRotation.x -= rotateSpeed
      rotationChanged = true
    }

    // 限制俯仰角度
    const maxPitch = Math.PI / 2 * 0.8
    currentRotation.x = Math.max(-maxPitch, Math.min(maxPitch, currentRotation.x))

    // 应用变化
    if (positionChanged) {
      this.freeCamera.setPosition(currentPosition.x, currentPosition.y, currentPosition.z)
    }
    if (rotationChanged) {
      this.freeCamera.setRotation(currentRotation.x, currentRotation.y, currentRotation.z)
    }
  }

  private setupScene() {
    this.scene.background = new THREE.Color(0xf0f0f0)
    this.scene.fog = new THREE.Fog(0xf0f0f0, 500, 10000)
  }

  private setupCamera() {
    this.camera.fov = 50  // 减小FOV，减少畸变
    this.camera.aspect = this.container.clientWidth / this.container.clientHeight
    this.camera.near = 1  // 增加近平面距离
    this.camera.far = 1000  // 减少远平面距离
    this.camera.position.set(30, 25, 30)  // 调整相机位置，更接近车辆
    this.camera.lookAt(0, 0, 0)
    this.camera.updateProjectionMatrix()  // 确保更新投影矩阵
  }

  private setupRenderer() {
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight)
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    this.renderer.outputColorSpace = THREE.SRGBColorSpace
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping  // 改善色调映射
    this.renderer.toneMappingExposure = 1.0
    this.container.appendChild(this.renderer.domElement)
  }

  private setupLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
    this.scene.add(ambientLight)

    // 主光源 - 白色
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(100, 100, 50)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    directionalLight.shadow.camera.near = 0.5
    directionalLight.shadow.camera.far = 500
    directionalLight.shadow.camera.left = -100
    directionalLight.shadow.camera.right = 100
    directionalLight.shadow.camera.top = 100
    directionalLight.shadow.camera.bottom = -100
    this.scene.add(directionalLight)

    // 补充光源
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.3)
    fillLight.position.set(-50, 50, -50)
    this.scene.add(fillLight)
  }

  private setupControls() {
    this.controls.enableDamping = true
    this.controls.dampingFactor = 0.08
    this.controls.screenSpacePanning = false
    this.controls.minDistance = 5   // 最小距离
    this.controls.maxDistance = 200 // 最大距离
    this.controls.maxPolarAngle = Math.PI / 2.2  // 限制垂直角度，避免过度俯视
    this.controls.minPolarAngle = Math.PI / 6    // 限制最小仰角

    // 设置目标点为车辆位置
    this.controls.target.set(0, 1, 0)  // 稍微抬高目标点
    this.controls.update()
  }

  private setupGround() {
    // 创建地面
    const groundGeometry = new THREE.PlaneGeometry(100, 100)  // 减小地面尺寸
    const groundMaterial = new THREE.MeshLambertMaterial({
      color: 0xf0f0f0,  // 更亮的地面颜色
      transparent: true,
      opacity: 0.9
    })
    const ground = new THREE.Mesh(groundGeometry, groundMaterial)
    ground.rotation.x = -Math.PI / 2
    ground.receiveShadow = true
    this.scene.add(ground)

    // 添加网格 - 更密集的网格，更好的空间感
    const gridHelper = new THREE.GridHelper(100, 50, 0x999999, 0xcccccc)
    this.scene.add(gridHelper)

    // 添加坐标轴 - 稍小一些
    const axesHelper = new THREE.AxesHelper(10)
    this.scene.add(axesHelper)
  }

  private async loadDefaultVehicle() {
    try {
      // 尝试加载GLB模型，如果失败则使用备用模型
      const vehicleModel = await this.modelLoader.loadVehicleModel('/car1.glb')
      // const box = new THREE.Box3().setFromObject(vehicleModel);
      // const size = box.getSize(new THREE.Vector3()); // 原始尺寸
      // const targetSize = new THREE.Vector3(18, 18, 60); // 目标长宽高
      // const scale = new THREE.Vector3(
      //     targetSize.x / size.x,
      //     targetSize.y / size.y,
      //     targetSize.z / size.z
      // );
      vehicleModel.name = 'car1'
      vehicleModel.position.set(0, 0, 0)
      vehicleModel.scale.set(0.01, 0.01, 0.01)
      // vehicleModel.scale.copy(scale)
      this.scene.add(vehicleModel)
      this.vehicleController.addVehicle('car1', vehicleModel)

      console.log('Vehicle loaded successfully')
    } catch (error) {
      console.error('Failed to load vehicle:', error)
    }
  }

  // 加载自定义GLB模型
  async loadCustomVehicle(glbUrl: string, vehicleId: string = 'car1') {
    try {
      // 移除现有车辆
      this.vehicleController.removeVehicle(vehicleId)

      // 加载新模型
      const vehicleModel = await this.modelLoader.loadVehicleModel(glbUrl)
      vehicleModel.name = vehicleId
      vehicleModel.position.set(0, 0, 0)

      this.scene.add(vehicleModel)
      this.vehicleController.addVehicle(vehicleId, vehicleModel)

      console.log(`Custom vehicle ${vehicleId} loaded from ${glbUrl}`)
      return true
    } catch (error) {
      console.error('Failed to load custom vehicle:', error)
      return false
    }
  }

  private animate = () => {
    this.animationId = requestAnimationFrame(this.animate)

    // Calculate delta time for smooth movement
    const currentTime = performance.now() / 1000
    const deltaTime = currentTime - this.lastFrameTime
    this.lastFrameTime = currentTime

    // Update controls based on camera mode
    if (!this.isFreeCameraMode && !this.isGlobalCameraMode) {
      this.controls.update()
    }

    // Sync free camera parameters with main camera to prevent distortion
    this.freeCamera.syncWithMainCamera(this.camera)

    // Update free camera
    this.freeCamera.update(deltaTime)

    // Update remote free camera control
    this.updateRemoteFreeCameraControl(deltaTime)

    // Update global camera
    this.globalCamera.update(deltaTime)

    // Trigger position update event for global camera
    if (this.isGlobalCameraMode) {
      const position = this.globalCamera.getPosition()
      this.container.dispatchEvent(new CustomEvent('globalCameraPositionChange', {
        detail: { x: position.x, y: position.y, z: position.z }
      }))
    }

    // Update vehicle controller
    this.vehicleController.update()

    // Render with appropriate camera
    const activeCamera = this.isFreeCameraMode ? this.freeCamera.getCamera() : this.camera
    this.renderer.render(this.scene, activeCamera)
  }

  private handleResize = () => {
    const onResize = () => {
      const width = this.container.clientWidth
      const height = this.container.clientHeight

      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
      this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))  // 限制像素比，提高性能

      // Update free camera viewport frame on resize
      if (this.freeCamera) {
        this.freeCamera.onWindowResize()
      }
    }

    window.addEventListener('resize', onResize)

    // 初始设置像素比
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
  }

  getVehicleController(): VehicleController {
    return this.vehicleController
  }

  // Free camera management methods
  private enableFreeCameraMode() {
    // Store original camera state
    this.originalCameraPosition.copy(this.camera.position)
    this.originalCameraRotation.copy(this.camera.rotation)

    // Disable orbit controls
    this.controls.enabled = false
  }

  private disableFreeCameraMode() {
    // Restore original camera state
    this.camera.position.copy(this.originalCameraPosition)
    this.camera.rotation.copy(this.originalCameraRotation)
    this.camera.updateProjectionMatrix()

    // Re-enable orbit controls
    this.controls.enabled = true
    this.controls.update()
  }



  // Public methods for camera control
  toggleFreeCamera(active: boolean) {
    this.freeCamera.setActive(active)
  }

  jumpToFreeCamera() {
    if (this.freeCamera) {
      const freeCameraState = this.freeCamera.getState()
      const freeCameraConfig = this.freeCamera.getConfig()

      // Copy position and rotation
      this.camera.position.copy(freeCameraState.position)
      this.camera.rotation.copy(freeCameraState.rotation)

      // Sync camera parameters to match free camera
      this.camera.fov = freeCameraConfig.fov
      this.camera.near = freeCameraConfig.near
      this.camera.far = freeCameraConfig.far
      this.camera.updateProjectionMatrix()

      // Update orbit controls target to look in the same direction
      const direction = new THREE.Vector3(0, 0, -1).applyEuler(freeCameraState.rotation)
      const target = freeCameraState.position.clone().add(direction.multiplyScalar(10))
      this.controls.target.copy(target)
      this.controls.update()
    }
  }

  resetFreeCamera() {
    this.freeCamera.setPosition(10, 10, 10)
    this.freeCamera.setRotation(0, 0, 0)
  }

  toggleFreeCameraVisibility() {
    const currentState = this.freeCamera.getState()
    this.freeCamera.setVisible(!currentState.isVisible)
  }

  updateFreeCameraConfig(config: Partial<CameraConfig>) {
    this.freeCamera.updateConfig(config)
  }

  getFreeCamera(): FreeCamera {
    return this.freeCamera
  }

  getContainer(): HTMLElement {
    return this.container
  }

  // 新增的自由相机控制方法
  setFreeCameraPosition(x: number, y: number, z: number, smooth: boolean = false) {
    if (this.freeCamera) {
      this.freeCamera.moveToPosition(x, y, z, { smooth, duration: 1000 })
    }
  }

  setFreeCameraRotation(x: number, y: number, z: number, smooth: boolean = false) {
    if (this.freeCamera) {
      this.freeCamera.rotateToAngles(x, y, z, { smooth, duration: 1000 })
    }
  }

  freeCameraLookAt(x: number, y: number, z: number, smooth: boolean = false) {
    if (this.freeCamera) {
      const target = new THREE.Vector3(x, y, z)
      this.freeCamera.lookAt(target, { smooth, duration: 1000 })
    }
  }

  freeCameraOrbitTarget(targetX: number, targetY: number, targetZ: number, radius: number, azimuth: number, elevation: number, smooth: boolean = true) {
    if (this.freeCamera) {
      const target = new THREE.Vector3(targetX, targetY, targetZ)
      this.freeCamera.orbitAroundTarget(target, radius, azimuth, elevation, { smooth, duration: 1500 })
    }
  }

  applyFreeCameraPreset(preset: CameraPreset, smooth: boolean = true) {
    if (this.freeCamera) {
      this.freeCamera.applyPreset(preset, { smooth, duration: 1200 })
    }
  }

  setFreeCameraMouseControl(enabled: boolean) {
    if (this.freeCamera) {
      this.freeCamera.setMouseControlEnabled(enabled)
    }
  }

  setFreeCameraMouseSensitivity(sensitivity: number) {
    if (this.freeCamera) {
      this.freeCamera.setMouseSensitivity(sensitivity)
    }
  }

  // 控制自由相机显示
  setFreeCameraSphereVisible(visible: boolean) {
    if (this.freeCamera) {
      this.freeCamera.setCameraSphereVisible(visible)
    }
  }

  setFreeCameraDirectionVisible(visible: boolean) {
    if (this.freeCamera) {
      this.freeCamera.setDirectionIndicatorVisible(visible)
    }
  }

  isFreeCameraSphereVisible(): boolean {
    return this.freeCamera ? this.freeCamera.isCameraSphereVisible() : true
  }

  isFreeCameraDirectionVisible(): boolean {
    return this.freeCamera ? this.freeCamera.isDirectionIndicatorVisible() : true
  }

  // FOV控制方法
  updateFreeCameraFOV(horizontalFOV: number, verticalFOV: number, aspectRatio: number) {
    if (this.freeCamera) {
      this.freeCamera.updateFOVSettings(horizontalFOV, verticalFOV, aspectRatio)
    }
  }

  // 设置精确FOV模式
  setFreeCameraPrecisionMode(enabled: boolean, forceAspectRatio: boolean = true) {
    if (this.freeCamera) {
      this.freeCamera.setPrecisionMode(enabled, forceAspectRatio)
    }
  }

  // 获取精确FOV模式状态
  getFreeCameraPrecisionMode(): { enabled: boolean; forceAspectRatio: boolean } {
    return this.freeCamera ? this.freeCamera.getPrecisionMode() : { enabled: true, forceAspectRatio: true }
  }

  // 视野框架控制方法
  setFreeCameraViewportFrame(enabled: boolean) {
    if (this.freeCamera) {
      this.freeCamera.setViewportFrameEnabled(enabled)
    }
  }

  getFreeCameraViewportFrameEnabled(): boolean {
    return this.freeCamera ? this.freeCamera.getViewportFrameEnabled() : false
  }

  updateFreeCameraViewportFrameConfig(config: any) {
    if (this.freeCamera) {
      this.freeCamera.updateViewportFrameConfig(config)
    }
  }

  getFreeCameraViewportFrameConfig(): any {
    return this.freeCamera ? this.freeCamera.getViewportFrameConfig() : null
  }

  // Enhanced viewport frame methods
  setFreeCameraViewportFrameScreenSpaceMode(enabled: boolean) {
    if (this.freeCamera) {
      this.freeCamera.setViewportFrameScreenSpaceMode(enabled)
    }
  }

  setFreeCameraViewportFrameMaintainAspectRatio(enabled: boolean) {
    if (this.freeCamera) {
      this.freeCamera.setViewportFrameMaintainAspectRatio(enabled)
    }
  }

  setFreeCameraViewportFrameScaleToFit(enabled: boolean) {
    if (this.freeCamera) {
      this.freeCamera.setViewportFrameScaleToFit(enabled)
    }
  }

  getFreeCameraViewportFrameDimensions(): { width: number; height: number } {
    return this.freeCamera ? this.freeCamera.getViewportFrameDimensions() : { width: 0, height: 0 }
  }

  getFreeCameraViewportFrameStatus() {
    return this.freeCamera ? this.freeCamera.getViewportFrameStatus() : null
  }

  getFreeCameraFOVSettings(): FOVSettings {
    return this.freeCamera ? this.freeCamera.getFOVSettings() : { horizontalFOV: 60, verticalFOV: 30, aspectRatio: 2.0 }
  }

  calculateFreeCameraFOVFromTarget(targetWidth: number, targetHeight: number, distance: number): FOVSettings {
    return this.freeCamera ? this.freeCamera.calculateFOVFromTarget(targetWidth, targetHeight, distance) : { horizontalFOV: 60, verticalFOV: 30, aspectRatio: 2.0 }
  }

  calculateFreeCameraDistanceFromFOV(targetWidth: number, targetHeight: number): { byWidth: number, byHeight: number, recommended: number } {
    return this.freeCamera ? this.freeCamera.calculateDistanceFromFOV(targetWidth, targetHeight) : { byWidth: 0, byHeight: 0, recommended: 0 }
  }

  // 重置自由相机FOV到默认值
  resetFreeCameraFOV() {
    if (this.freeCamera) {
      this.freeCamera.resetFOVToDefault()
    }
  }

  // 应用窗口长宽比到自由相机
  applyWindowAspectToFreeCamera(windowAspect: number) {
    if (this.freeCamera) {
      this.freeCamera.applyWindowAspectRatio(windowAspect)
    }
  }

  // 屏幕适配控制
  setFreeCameraScreenAdaptation(enabled: boolean, mode: string = 'fit', priority: string = 'auto') {
    if (this.freeCamera) {
      this.freeCamera.setScreenAdaptation(enabled, mode, priority)
    }
  }

  getFreeCameraScreenAdaptation() {
    return this.freeCamera ? this.freeCamera.getScreenAdaptation() : { enabled: false, mode: 'fit', priority: 'auto' }
  }

  // 更新屏幕尺寸
  updateFreeCameraScreenAspect() {
    if (this.freeCamera) {
      this.freeCamera.updateScreenAspect()
    }
  }



  // 远程自由相机控制方法
  enableRemoteFreeCameraControl(enabled: boolean) {
    this.isRemoteFreeCameraControlEnabled = enabled
  }

  isRemoteFreeCameraControlActive(): boolean {
    return this.isRemoteFreeCameraControlEnabled
  }

  getRemoteControlKeyMappings(): { [key: string]: string } {
    return {
      'KeyW': '自由相机前进',
      'KeyS': '自由相机后退',
      'KeyA': '自由相机左移',
      'KeyD': '自由相机右移',
      'KeyQ': '自由相机上升',
      'KeyE': '自由相机下降',
      'ArrowLeft': '自由相机左转',
      'ArrowRight': '自由相机右转',
      'ArrowUp': '自由相机上仰',
      'ArrowDown': '自由相机下俯'
    }
  }

  // Global camera management methods
  toggleGlobalCamera(active: boolean) {
    this.isGlobalCameraMode = active
    this.globalCamera.setActive(active)

    if (active) {
      // Disable other camera modes
      this.isFreeCameraMode = false
      this.freeCamera.setActive(false)
      this.controls.enabled = false

      // Set initial global perspective
      this.globalCamera.setGlobalPerspective()
    } else {
      // Re-enable orbit controls
      this.controls.enabled = true
    }
  }

  resetToGlobalView() {
    if (this.globalCamera) {
      this.globalCamera.resetToGlobalView()
    }
  }

  setTopDownView() {
    if (this.globalCamera) {
      this.globalCamera.setGlobalPerspective(100, -Math.PI / 2) // 完全俯视
    }
  }

  setIsometricView() {
    if (this.globalCamera) {
      this.globalCamera.setGlobalPerspective(80, -Math.PI / 3) // 等距视角
    }
  }

  updateGlobalCameraConfig(config: Partial<GlobalCameraConfig>) {
    this.globalCamera.updateConfig(config)
  }

  getGlobalCamera(): GlobalCamera {
    return this.globalCamera
  }

  // ===== 多激光控制系统方法 =====

  // 创建新激光
  createNewLaser(name?: string): string {
    return this.freeCameraManager.createFreeCamera(name)
  }

  // 删除激光
  deleteLaser(id: string): boolean {
    return this.freeCameraManager.deleteFreeCamera(id)
  }

  // 重命名激光
  renameLaser(id: string, name: string): boolean {
    return this.freeCameraManager.renameFreeCamera(id, name)
  }

  // 激活激光
  activateLaser(id: string): boolean {
    return this.freeCameraManager.setActiveCamera(id)
  }

  // 切换激光可见性
  toggleLaserVisibility(id: string): boolean {
    return this.freeCameraManager.toggleCameraVisibility(id)
  }

  // 跳转到激光视角
  jumpToLaser(id: string): boolean {
    const camera = this.freeCameraManager.getCamera(id)
    if (!camera) return false

    // 激活激光
    this.freeCameraManager.setActiveCamera(id)

    // 切换到自由相机模式
    this.isFreeCameraMode = true
    this.enableFreeCameraMode()

    return true
  }

  // 获取所有激光列表
  getAllLasers(): FreeCameraInstance[] {
    return this.freeCameraManager.getAllCameras()
  }

  // 设置选中的激光
  setSelectedLasers(laserIds: string[]): void {
    this.selectedLasers = [...laserIds]
    console.log('ThreeManager: 设置选中激光:', this.selectedLasers)
  }

  // 获取选中的激光
  getSelectedLasers(): string[] {
    return [...this.selectedLasers]
  }

  // 设置控制模式
  setControlMode(mode: string): void {
    this.controlMode = mode
  }

  // 获取控制模式
  getControlMode(): string {
    return this.controlMode
  }

  // 批量操作
  batchOperation(operation: string, laserIds: string[]): void {
    switch (operation) {
      case 'toggleVisibility':
        laserIds.forEach(id => this.toggleLaserVisibility(id))
        break
      case 'enableViewportFrame':
        laserIds.forEach(id => this.freeCameraManager.setCameraViewportFrame(id, true))
        break
      case 'disableViewportFrame':
        laserIds.forEach(id => this.freeCameraManager.setCameraViewportFrame(id, false))
        break
      case 'resetPosition':
        laserIds.forEach(id => {
          this.freeCameraManager.setCameraPosition(id, 10, 10, 10)
          this.freeCameraManager.setCameraRotation(id, 0, 0, 0)
        })
        break
    }
  }

  // 获取激光管理器
  getFreeCameraManager(): FreeCameraManager {
    return this.freeCameraManager
  }

  dispose() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    window.removeEventListener('resize', this.handleResize)

    // Remove remote control event listeners
    if (this.remoteControlKeyDownHandler) {
      window.removeEventListener('keydown', this.remoteControlKeyDownHandler)
    }
    if (this.remoteControlKeyUpHandler) {
      window.removeEventListener('keyup', this.remoteControlKeyUpHandler)
    }

    this.controls.dispose()
    this.freeCamera.dispose()
    this.freeCameraManager.dispose()
    this.globalCamera.dispose()
    this.renderer.dispose()
    if (this.container.contains(this.renderer.domElement)) {
      this.container.removeChild(this.renderer.domElement)
    }
  }
}
