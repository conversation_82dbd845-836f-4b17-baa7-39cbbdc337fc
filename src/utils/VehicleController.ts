import * as THREE from 'three'

interface VehicleMovement {
  vehicleId: string
  targetPosition?: THREE.Vector3
  targetRotation?: number
  speed: number
  isMoving: boolean
  startPosition?: THREE.Vector3
  startRotation?: number
  progress: number
  duration: number
}

interface TrackRotation {
  vehicleId: string
  trackNames: string[]
  rotationSpeed: number
  isRotating: boolean
  totalRotation: number
}

export class VehicleController {
  private scene: THREE.Scene
  private vehicles: Map<string, THREE.Group> = new Map()
  private movements: Map<string, VehicleMovement> = new Map()
  private trackRotations: Map<string, TrackRotation> = new Map()
  private clock: THREE.Clock = new THREE.Clock()

  constructor(scene: THREE.Scene) {
    this.scene = scene
  }

  addVehicle(id: string, vehicleGroup: THREE.Group) {
    this.vehicles.set(id, vehicleGroup)
    vehicleGroup.userData.id = id
  }

  removeVehicle(id: string) {
    const vehicle = this.vehicles.get(id)
    if (vehicle) {
      this.scene.remove(vehicle)
      this.vehicles.delete(id)
      this.movements.delete(id)
      this.trackRotations.delete(id)
    }
  }

  moveVehicleToPosition(vehicleId: string, x: number, y: number, z: number, speed: number = 2) {
    const vehicle = this.vehicles.get(vehicleId)
    if (!vehicle) {
      console.warn(`Vehicle ${vehicleId} not found`)
      return
    }

    const targetPosition = new THREE.Vector3(x, y, z)
    const currentPosition = vehicle.position.clone()
    const distance = currentPosition.distanceTo(targetPosition)
    const duration = distance / speed

    this.movements.set(vehicleId, {
      vehicleId,
      targetPosition,
      speed,
      isMoving: true,
      startPosition: currentPosition,
      progress: 0,
      duration
    })

    console.log(`Moving vehicle ${vehicleId} to position (${x}, ${y}, ${z}) at speed ${speed}`)
  }

  moveVehicleByDirection(vehicleId: string, direction: string, distance: number, speed: number = 2) {
    const vehicle = this.vehicles.get(vehicleId)
    if (!vehicle) {
      console.warn(`Vehicle ${vehicleId} not found`)
      return
    }

    const currentPosition = vehicle.position.clone()
    const currentRotation = vehicle.rotation.y
    let targetPosition = currentPosition.clone()

    switch (direction) {
      case 'forward':
        targetPosition.x += Math.sin(currentRotation) * distance
        targetPosition.z += Math.cos(currentRotation) * distance
        break
      case 'backward':
        targetPosition.x -= Math.sin(currentRotation) * distance
        targetPosition.z -= Math.cos(currentRotation) * distance
        break
      case 'left':
        targetPosition.x += Math.cos(currentRotation) * distance
        targetPosition.z -= Math.sin(currentRotation) * distance
        break
      case 'right':
        targetPosition.x -= Math.cos(currentRotation) * distance
        targetPosition.z += Math.sin(currentRotation) * distance
        break
    }

    this.moveVehicleToPosition(vehicleId, targetPosition.x, targetPosition.y, targetPosition.z, speed)
  }

  rotateVehicle(vehicleId: string, direction: string, angle: number, speed: number = 1) {
    const vehicle = this.vehicles.get(vehicleId)
    if (!vehicle) {
      console.warn(`Vehicle ${vehicleId} not found`)
      return
    }

    const currentRotation = vehicle.rotation.y
    let targetRotation = currentRotation

    switch (direction) {
      case 'left':
        targetRotation += THREE.MathUtils.degToRad(angle)
        break
      case 'right':
        targetRotation -= THREE.MathUtils.degToRad(angle)
        break
    }

    const rotationDiff = Math.abs(targetRotation - currentRotation)
    const duration = rotationDiff / speed

    this.movements.set(vehicleId, {
      vehicleId,
      targetRotation,
      speed,
      isMoving: true,
      startRotation: currentRotation,
      progress: 0,
      duration
    })

    console.log(`Rotating vehicle ${vehicleId} ${direction} by ${angle} degrees`)
  }

  resetVehiclePosition(vehicleId: string) {
    const vehicle = this.vehicles.get(vehicleId)
    if (!vehicle) {
      console.warn(`Vehicle ${vehicleId} not found`)
      return
    }

    vehicle.position.set(0, 0, 0)
    vehicle.rotation.set(0, 0, 0)
    this.movements.delete(vehicleId)
    this.trackRotations.delete(vehicleId)

    // 重置履带旋转
    this.resetTrackRotations(vehicle)

    console.log(`Reset vehicle ${vehicleId} position`)
  }

  // 重置履带旋转状态
  private resetTrackRotations(vehicle: THREE.Group) {
    vehicle.traverse((child) => {
      if (child.userData.rotationGroup) {
        // 重置旋转组的旋转
        child.userData.rotationGroup.rotation.set(0, 0, 0)
      } else if (child.name && (
        child.name.includes('履带') ||
        child.name.includes('track') ||
        child.name.includes('Track')
      )) {
        // 重置履带自身的旋转
        child.rotation.set(0, 0, 0)
      }
    })
  }

  // 履带控制方法
  startTrackRotation(vehicleId: string, trackNames: string[] = ['履带1', '履带2', '履带3'], rotationSpeed: number = 1) {
    const vehicle = this.vehicles.get(vehicleId)
    if (!vehicle) {
      console.warn(`Vehicle ${vehicleId} not found`)
      return
    }

    // 验证履带是否存在并设置旋转中心
    const foundTracks: THREE.Object3D[] = []
    const validTrackNames: string[] = []

    trackNames.forEach(trackName => {
      const track = this.findTrackByName(vehicle, trackName)
      if (track) {
        foundTracks.push(track)
        validTrackNames.push(trackName)
        console.log(`Found and configured track: ${trackName}`)
      } else {
        console.warn(`Track ${trackName} not found in vehicle ${vehicleId}`)
      }
    })

    if (foundTracks.length === 0) {
      console.warn(`No valid tracks found for vehicle ${vehicleId}`)
      return
    }

    this.trackRotations.set(vehicleId, {
      vehicleId,
      trackNames: validTrackNames,
      rotationSpeed,
      isRotating: true,
      totalRotation: 0
    })

    console.log(`Started track rotation for vehicle ${vehicleId}, tracks: ${validTrackNames.join(', ')}, speed: ${rotationSpeed}`)
  }

  stopTrackRotation(vehicleId: string) {
    const trackRotation = this.trackRotations.get(vehicleId)
    if (trackRotation) {
      trackRotation.isRotating = false
      console.log(`Stopped track rotation for vehicle ${vehicleId}`)
    }
  }

  setTrackRotationSpeed(vehicleId: string, rotationSpeed: number) {
    const trackRotation = this.trackRotations.get(vehicleId)
    if (trackRotation) {
      trackRotation.rotationSpeed = rotationSpeed
      console.log(`Set track rotation speed for vehicle ${vehicleId} to ${rotationSpeed}`)
    }
  }

  // 查找履带对象并设置旋转中心
  private findTrackByName(vehicle: THREE.Group, trackName: string): THREE.Object3D | null {
    let foundTrack: THREE.Object3D | null = null

    vehicle.traverse((child) => {
      if (child.name === trackName ||
          child.name.includes(trackName) ||
          trackName.includes(child.name)) {
        foundTrack = child

        // 确保履带以自己的中心点旋转
        this.setupTrackRotationCenter(child)
      }
    })

    return foundTrack
  }

  // 设置履带的旋转中心
  private setupTrackRotationCenter(track: THREE.Object3D) {
    // 如果履带还没有设置过旋转中心，则计算并设置
    if (!track.userData.rotationCenterSet) {
      // 计算履带的包围盒（在世界坐标系中）
      const box = new THREE.Box3().setFromObject(track)
      const center = box.getCenter(new THREE.Vector3())

      // 获取履带的父对象
      const parent = track.parent
      if (parent) {
        // 将世界坐标转换为父对象的本地坐标
        const localCenter = parent.worldToLocal(center.clone())

        // 创建一个新的组作为旋转中心
        const rotationGroup = new THREE.Group()
        rotationGroup.name = `${track.name}_RotationGroup`

        // 将旋转组放置在履带的中心位置（父对象的本地坐标）
        rotationGroup.position.copy(localCenter)

        // 保存履带的原始位置
        const originalPosition = track.position.clone()

        // 将履带从原父对象中移除
        parent.remove(track)

        // 计算履带相对于旋转中心的偏移
        const offset = originalPosition.sub(localCenter)
        track.position.copy(offset)

        // 将履带添加到旋转组
        rotationGroup.add(track)

        // 将旋转组添加到原父对象
        parent.add(rotationGroup)

        // 更新履带的引用为旋转组
        track.userData.rotationGroup = rotationGroup
        track.userData.rotationCenterSet = true
        track.userData.originalParent = parent

        console.log(`Set rotation center for track: ${track.name}`, {
          worldCenter: center,
          localCenter: localCenter,
          offset: offset
        })
      }
    }
  }

  // 获取车辆的所有履带
  getVehicleTracks(vehicleId: string): string[] {
    const vehicle = this.vehicles.get(vehicleId)
    if (!vehicle) {
      return []
    }

    const trackNames: string[] = []
    vehicle.traverse((child) => {
      if (child.name && (
        child.name.includes('履带') ||
        child.name.includes('track') ||
        child.name.includes('Track')
      )) {
        trackNames.push(child.name)
      }
    })

    return trackNames
  }

  update() {
    const deltaTime = this.clock.getDelta()

    // 处理车辆移动
    this.movements.forEach((movement, vehicleId) => {
      if (!movement.isMoving) return

      const vehicle = this.vehicles.get(vehicleId)
      if (!vehicle) return

      movement.progress += deltaTime / movement.duration

      if (movement.progress >= 1) {
        // 移动完成
        movement.progress = 1
        movement.isMoving = false

        if (movement.targetPosition) {
          vehicle.position.copy(movement.targetPosition)
        }
        if (movement.targetRotation !== undefined) {
          vehicle.rotation.y = movement.targetRotation
        }

        this.movements.delete(vehicleId)
        console.log(`Vehicle ${vehicleId} movement completed`)
      } else {
        // 插值移动
        if (movement.targetPosition && movement.startPosition) {
          const currentPos = movement.startPosition.clone().lerp(movement.targetPosition, movement.progress)
          vehicle.position.copy(currentPos)
        }
        if (movement.targetRotation !== undefined && movement.startRotation !== undefined) {
          vehicle.rotation.y = THREE.MathUtils.lerp(movement.startRotation, movement.targetRotation, movement.progress)
        }
      }
    })

    // 处理履带旋转
    this.trackRotations.forEach((trackRotation, vehicleId) => {
      if (!trackRotation.isRotating) return

      const vehicle = this.vehicles.get(vehicleId)
      if (!vehicle) return

      const rotationDelta = trackRotation.rotationSpeed * deltaTime

      trackRotation.trackNames.forEach(trackName => {
        const track = this.findTrackByName(vehicle, trackName)
        if (track) {
          // 使用旋转组进行旋转，如果没有旋转组则直接旋转履带
          const rotationTarget = track.userData.rotationGroup || track
          rotationTarget.rotation.y += rotationDelta
          trackRotation.totalRotation += rotationDelta
        }
      })
    })
  }

  getVehiclePosition(vehicleId: string): THREE.Vector3 | null {
    const vehicle = this.vehicles.get(vehicleId)
    return vehicle ? vehicle.position.clone() : null
  }

  getVehicleRotation(vehicleId: string): number | null {
    const vehicle = this.vehicles.get(vehicleId)
    return vehicle ? vehicle.rotation.y : null
  }

  isVehicleMoving(vehicleId: string): boolean {
    const movement = this.movements.get(vehicleId)
    return movement ? movement.isMoving : false
  }

  getAllVehicles(): string[] {
    return Array.from(this.vehicles.keys())
  }

  // 履带状态查询方法
  isTrackRotating(vehicleId: string): boolean {
    const trackRotation = this.trackRotations.get(vehicleId)
    return trackRotation ? trackRotation.isRotating : false
  }

  getTrackRotationSpeed(vehicleId: string): number {
    const trackRotation = this.trackRotations.get(vehicleId)
    return trackRotation ? trackRotation.rotationSpeed : 0
  }

  getTrackTotalRotation(vehicleId: string): number {
    const trackRotation = this.trackRotations.get(vehicleId)
    return trackRotation ? trackRotation.totalRotation : 0
  }

  getTrackStatus(vehicleId: string) {
    const trackRotation = this.trackRotations.get(vehicleId)
    if (!trackRotation) {
      return null
    }

    return {
      isRotating: trackRotation.isRotating,
      rotationSpeed: trackRotation.rotationSpeed,
      totalRotation: trackRotation.totalRotation,
      trackNames: trackRotation.trackNames
    }
  }
}
