import * as THREE from 'three'

export interface ViewportFrameConfig {
  enabled: boolean
  horizontalFOV: number
  verticalFOV: number
  distance: number // 框架距离相机的距离
  frameColor: number
  frameOpacity: number
  maskColor: number
  maskOpacity: number
  showFrame: boolean // 是否显示框架边界
  // New properties for enhanced masking
  useScreenSpaceMask: boolean // Use CSS overlay instead of 3D planes
  maintainAspectRatio: boolean // Maintain viewport aspect ratio
  scaleToFit: boolean // Scale camera view to fit viewport
  // 视锥相关配置
  enableViewportCone: boolean // 是否启用3D视锥遮挡
  coneColor: number // 视锥颜色
  coneOpacity: number // 视锥透明度
  coneLength: number // 视锥长度（从相机到蒙板的距离）
  // 裁剪相关配置
  enableClipping: boolean // 是否启用裁剪
}

export class ViewportFrame {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private config: ViewportFrameConfig
  private renderer: THREE.WebGLRenderer

  // 视野框架组件
  private frameGroup: THREE.Group
  private viewportFrame: THREE.Mesh | null = null
  private maskPlanes: THREE.Mesh[] = []
  private isInitialized: boolean = false

  // 视锥组件
  private viewportCone: THREE.Mesh | null = null
  private coneGroup: THREE.Group
  // 聚光灯
  private spotLight: THREE.SpotLight | null = null
  private spotLightTarget: THREE.Object3D | null = null

  // 新增属性
  private clippingPlanes: THREE.Plane[] = []
  private clippingEnabled: boolean = false

  constructor(scene: THREE.Scene, camera: THREE.PerspectiveCamera, renderer: THREE.WebGLRenderer) {
    this.scene = scene
    this.camera = camera
    this.renderer = renderer
    this.originalCameraFOV = camera.fov

    // 默认配置 - 优化用于视野模拟
    this.config = {
      enabled: true, // 默认启用视野模拟
      horizontalFOV: 60,
      verticalFOV: 30,
      distance: 5, // 框架距离相机5个单位
      frameColor: 0xffffff,
      frameOpacity: 0.8,
      maskColor: 0x808080, // 灰色蒙板
      maskOpacity: 0.5, // 增加蒙板透明度
      showFrame: false, // 默认不显示框架边界，只显示蒙板
      // Enhanced masking properties
      useScreenSpaceMask: true, // 使用屏幕空间蒙板
      maintainAspectRatio: true, // 保持长宽比
      scaleToFit: false, // 不缩放相机视图，保持原始渲染
      // 视锥相关配置
      enableViewportCone: true, // 默认启用3D视锥遮挡
      coneColor: 0x000000, // 视锥颜色：纯黑色
      coneOpacity: 0.3, // 视锥透明度
      coneLength: 10, // 视锥长度
      // 裁剪相关配置
      enableClipping: false // 默认不启用裁剪
    }
    
    this.frameGroup = new THREE.Group()
    this.frameGroup.name = 'ViewportFrame'
    this.coneGroup = new THREE.Group()
    this.coneGroup.name = 'ViewportCone'
    this.init()
  }
  
  private init() {
    this.scene.add(this.frameGroup)
    this.scene.add(this.coneGroup)
    this.isInitialized = true
    this.updateFrame()
  }
  
  // 更新视野框架
  updateFrame() {
    if (!this.isInitialized) return

    // 清除现有的框架和蒙板
    this.clearFrame()

    if (!this.config.enabled) {
      this.restoreCameraFOV()
      return
    }

    if (this.config.useScreenSpaceMask) {
      // Use screen-space masking for better performance and accuracy
      // 在视野模拟模式下，不修改相机FOV，保持相机状态稳定
      // this.adjustCameraForViewport() // 已移除，避免修改相机FOV
    } else {
      // Use traditional 3D plane masking
      const frameSize = this.calculateFrameSize()

      if (this.config.showFrame) {
        this.createViewportFrame(frameSize)
      }

      this.createMaskPlanes(frameSize)
      this.updateFramePosition()
    }

    // 创建视锥（无论使用哪种蒙板模式）
    if (this.config.enableViewportCone) {
      this.createViewportConeAndLight()
    }

    // 动态生成4个裁剪平面
    this.updateClippingPlanes()
  }
  
  // 计算框架尺寸
  private calculateFrameSize(): { width: number; height: number } {
    const distance = this.config.distance
    const horizontalRad = this.config.horizontalFOV * Math.PI / 180
    const verticalRad = this.config.verticalFOV * Math.PI / 180
    
    // 使用正切函数计算框架在指定距离处的实际尺寸
    const width = 2 * distance * Math.tan(horizontalRad / 2)
    const height = 2 * distance * Math.tan(verticalRad / 2)
    
    return { width, height }
  }
  
  // 创建视野框架边界
  private createViewportFrame(frameSize: { width: number; height: number }) {
    const { width, height } = frameSize
    const frameThickness = 0.02
    
    // 创建框架几何体（使用线框）
    const frameGeometry = new THREE.EdgesGeometry(
      new THREE.PlaneGeometry(width, height)
    )
    
    const frameMaterial = new THREE.LineBasicMaterial({
      color: this.config.frameColor,
      opacity: this.config.frameOpacity,
      transparent: true,
      linewidth: 2
    })
    
    this.viewportFrame = new THREE.LineSegments(frameGeometry, frameMaterial)
    this.viewportFrame.position.z = -this.config.distance
    this.frameGroup.add(this.viewportFrame)
  }
  
  // 创建蒙板平面
  private createMaskPlanes(frameSize: { width: number; height: number }) {
    const { width, height } = frameSize
    const screenAspect = window.innerWidth / window.innerHeight
    const fovAspect = width / height
    
    // 计算屏幕在框架距离处的尺寸
    const screenVerticalFOV = this.camera.fov * Math.PI / 180
    const screenHeight = 2 * this.config.distance * Math.tan(screenVerticalFOV / 2)
    const screenWidth = screenHeight * screenAspect
    
    const maskMaterial = new THREE.MeshBasicMaterial({
      color: this.config.maskColor,
      opacity: this.config.maskOpacity,
      transparent: true,
      side: THREE.DoubleSide
    })
    
    // 如果框架比屏幕小，需要创建蒙板
    if (width < screenWidth || height < screenHeight) {
      // 左右蒙板
      if (width < screenWidth) {
        const sideWidth = (screenWidth - width) / 2
        
        // 左蒙板
        const leftMaskGeometry = new THREE.PlaneGeometry(sideWidth, screenHeight)
        const leftMask = new THREE.Mesh(leftMaskGeometry, maskMaterial)
        leftMask.position.set(-(width + sideWidth) / 2, 0, -this.config.distance)
        this.maskPlanes.push(leftMask)
        this.frameGroup.add(leftMask)
        
        // 右蒙板
        const rightMaskGeometry = new THREE.PlaneGeometry(sideWidth, screenHeight)
        const rightMask = new THREE.Mesh(rightMaskGeometry, maskMaterial)
        rightMask.position.set((width + sideWidth) / 2, 0, -this.config.distance)
        this.maskPlanes.push(rightMask)
        this.frameGroup.add(rightMask)
      }
      
      // 上下蒙板
      if (height < screenHeight) {
        const topBottomWidth = Math.min(width, screenWidth)
        const sideHeight = (screenHeight - height) / 2
        
        // 上蒙板
        const topMaskGeometry = new THREE.PlaneGeometry(topBottomWidth, sideHeight)
        const topMask = new THREE.Mesh(topMaskGeometry, maskMaterial)
        topMask.position.set(0, (height + sideHeight) / 2, -this.config.distance)
        this.maskPlanes.push(topMask)
        this.frameGroup.add(topMask)
        
        // 下蒙板
        const bottomMaskGeometry = new THREE.PlaneGeometry(topBottomWidth, sideHeight)
        const bottomMask = new THREE.Mesh(bottomMaskGeometry, maskMaterial)
        bottomMask.position.set(0, -(height + sideHeight) / 2, -this.config.distance)
        this.maskPlanes.push(bottomMask)
        this.frameGroup.add(bottomMask)
      }
    }
  }
  
  // 清除框架和蒙板
  private clearFrame() {
    if (this.viewportFrame) {
      this.frameGroup.remove(this.viewportFrame)
      this.viewportFrame.geometry.dispose()
      if (Array.isArray(this.viewportFrame.material)) {
        this.viewportFrame.material.forEach(mat => mat.dispose())
      } else {
        this.viewportFrame.material.dispose()
      }
      this.viewportFrame = null
    }
    
    this.maskPlanes.forEach(mask => {
      this.frameGroup.remove(mask)
      mask.geometry.dispose()
      if (Array.isArray(mask.material)) {
        mask.material.forEach(mat => mat.dispose())
      } else {
        mask.material.dispose()
      }
    })
    this.maskPlanes = []

    // 清除视锥
    if (this.viewportCone) {
      this.coneGroup.remove(this.viewportCone)
      this.viewportCone.geometry.dispose()
      if (Array.isArray(this.viewportCone.material)) {
        this.viewportCone.material.forEach(mat => mat.dispose())
      } else {
        this.viewportCone.material.dispose()
      }
      this.viewportCone = null
    }

    // 清理聚光灯和聚光灯target
    if (this.spotLight) {
      this.coneGroup.remove(this.spotLight)
      this.spotLight = null
    }
    if (this.spotLightTarget) {
      this.coneGroup.remove(this.spotLightTarget)
      this.spotLightTarget = null
    }

    // 清理裁剪平面
    this.renderer.clippingPlanes = []
    this.clippingPlanes = []
  }
  
  // 更新框架位置（跟随相机）
  updateFramePosition() {
    if (!this.config.enabled || !this.isInitialized) return

    // 框架组跟随相机位置和旋转
    this.frameGroup.position.copy(this.camera.position)
    this.frameGroup.rotation.copy(this.camera.rotation)
    
    // 更新视锥位置和方向
    this.updateConeAndLightPosition()

    // 更新裁剪平面
    this.updateClippingPlanes()
  }

  // 创建视锥几何体
  private createViewportCone() {
    if (!this.config.enableViewportCone) return

    // 计算视锥的四个底面顶点（基于蒙板尺寸）
    const frameSize = this.calculateFrameSize()
    const { width, height } = frameSize
    
    // 创建视锥几何体 - 使用自定义几何体创建四棱锥
    const coneGeometry = this.createConeGeometry(width, height, this.config.coneLength)
    
    const coneMaterial = new THREE.MeshBasicMaterial({
      color: this.config.coneColor,
      opacity: this.config.coneOpacity,
      transparent: true,
      side: THREE.DoubleSide,
      depthWrite: false, // 避免深度写入问题
      depthTest: true
    })
    
    this.viewportCone = new THREE.Mesh(coneGeometry, coneMaterial)
    this.coneGroup.add(this.viewportCone)
  }

  // 创建四棱锥几何体
  private createConeGeometry(width: number, height: number, length: number): THREE.BufferGeometry {
    const geometry = new THREE.BufferGeometry()
    
    // 视锥顶点：相机位置（原点）
    const apex = new THREE.Vector3(0, 0, 0)
    
    // 视锥底面四个顶点（在蒙板平面上）
    const halfWidth = width / 2
    const halfHeight = height / 2
    const base1 = new THREE.Vector3(-halfWidth, -halfHeight, -length) // 左下
    const base2 = new THREE.Vector3(halfWidth, -halfHeight, -length)  // 右下
    const base3 = new THREE.Vector3(halfWidth, halfHeight, -length)   // 右上
    const base4 = new THREE.Vector3(-halfWidth, halfHeight, -length)  // 左上
    
    // 创建顶点数组
    const vertices = [
      // 底面四个顶点
      base1.x, base1.y, base1.z,
      base2.x, base2.y, base2.z,
      base3.x, base3.y, base3.z,
      base4.x, base4.y, base4.z,
      // 顶点
      apex.x, apex.y, apex.z
    ]
    
    // 创建面索引（三角形）
    const indices = [
      // 底面（两个三角形组成矩形）
      0, 1, 2, 0, 2, 3,
      // 四个侧面
      4, 0, 1, // 前面
      4, 1, 2, // 右面
      4, 2, 3, // 后面
      4, 3, 0  // 左面
    ]
    
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3))
    geometry.setIndex(indices)
    geometry.computeVertexNormals()
    
    return geometry
  }

  // 更新视锥位置和方向
  private updateConeAndLightPosition() {
    if (!this.config.enableViewportCone) return

    // 视锥组跟随相机位置和旋转
    this.coneGroup.position.copy(this.camera.position)
    this.coneGroup.rotation.copy(this.camera.rotation)

    // 动态同步聚光灯参数
    if (this.spotLight && this.spotLightTarget) {
      const frameSize = this.calculateFrameSize()
      const { width } = frameSize
      const length = this.config.coneLength
      this.spotLight.angle = Math.atan(width / (2 * length)) * 2
      this.spotLight.distance = 30
      this.spotLight.target.position.set(0, 0, -length)
      this.spotLight.color.set(0xff0000) // 聚光灯颜色：红色
      this.spotLight.intensity = 1000 // 强度增强
    }
  }

  // 更新配置
  updateConfig(newConfig: Partial<ViewportFrameConfig>) {
    Object.assign(this.config, newConfig)
    this.updateFrame()
  }
  
  // 设置FOV
  setFOV(horizontalFOV: number, verticalFOV: number) {
    this.config.horizontalFOV = horizontalFOV
    this.config.verticalFOV = verticalFOV
    this.updateFrame()
  }
  
  // 启用/禁用框架
  setEnabled(enabled: boolean) {
    this.config.enabled = enabled
    this.updateFrame()
  }
  
  // 获取配置
  getConfig(): ViewportFrameConfig {
    return { ...this.config }
  }
  
  // 更新（在渲染循环中调用）
  update() {
    if (this.config.enabled) {
      if (this.config.useScreenSpaceMask) {
        // 在视野模拟模式下，不修改相机FOV，保持相机状态稳定
        // this.adjustCameraForViewport() // 已移除，避免修改相机FOV
      } else {
        this.updateFramePosition()
      }
    }
  }

  // Handle window resize
  onWindowResize() {
    if (this.config.enabled && this.config.useScreenSpaceMask) {
      // 在视野模拟模式下，不修改相机FOV，保持相机状态稳定
      // this.adjustCameraForViewport() // 已移除，避免修改相机FOV
    }
  }

  // Get current viewport dimensions for external use
  getViewportDimensions(): { width: number; height: number } {
    return { ...this.viewportDimensions }
  }

  // Set masking mode
  setScreenSpaceMaskMode(enabled: boolean) {
    this.config.useScreenSpaceMask = enabled
    this.updateFrame()
  }

  // Set aspect ratio maintenance
  setMaintainAspectRatio(enabled: boolean) {
    this.config.maintainAspectRatio = enabled
    if (this.config.enabled) {
      this.updateFrame()
    }
  }

  // Set scale to fit mode
  setScaleToFit(enabled: boolean) {
    this.config.scaleToFit = enabled
    if (this.config.enabled) {
      this.updateFrame()
    }
  }

  // Set viewport cone mode
  setViewportCone(enabled: boolean) {
    this.config.enableViewportCone = enabled
    if (this.config.enabled) {
      this.updateFrame()
    }
  }

  // Set cone properties
  setConeProperties(color: number, opacity: number, length: number) {
    this.config.coneColor = color
    this.config.coneOpacity = opacity
    this.config.coneLength = length
    if (this.config.enabled && this.config.enableViewportCone) {
      this.updateFrame()
    }
  }

  // Set clipping enabled
  setClippingEnabled(enabled: boolean) {
    this.config.enableClipping = enabled
    this.updateClippingPlanes()
  }

  // Get viewport status information
  getViewportStatus(): {
    enabled: boolean;
    mode: 'screen-space' | '3d-planes';
    aspectRatio: number;
    scaleFactor: number;
    isClipped: boolean;
    effectiveViewport: { width: number; height: number };
    canvasSize: { width: number; height: number };
  } {
    const canvas = this.renderer.domElement
    const canvasWidth = canvas.clientWidth
    const canvasHeight = canvas.clientHeight

    const horizontalRad = this.config.horizontalFOV * Math.PI / 180
    const verticalRad = this.config.verticalFOV * Math.PI / 180
    const aspectRatio = Math.tan(horizontalRad / 2) / Math.tan(verticalRad / 2)

    let scaleFactor = 1
    if (this.config.scaleToFit && this.config.enabled) {
      const scaleX = canvasWidth / this.viewportDimensions.width
      const scaleY = canvasHeight / this.viewportDimensions.height
      scaleFactor = Math.min(scaleX, scaleY)
    }

    const isClipped = this.viewportDimensions.width < canvasWidth || this.viewportDimensions.height < canvasHeight

    return {
      enabled: this.config.enabled,
      mode: this.config.useScreenSpaceMask ? 'screen-space' : '3d-planes',
      aspectRatio,
      scaleFactor,
      isClipped,
      effectiveViewport: { ...this.viewportDimensions },
      canvasSize: { width: canvasWidth, height: canvasHeight }
    }
  }
  


  // 动态生成4个裁剪平面
  private updateClippingPlanes() {
    if (!this.config.enableClipping) {
      this.renderer.clippingPlanes = []
      this.clippingPlanes = []
      return
    }
    // 计算视锥底面4个点（世界坐标）
    const frameSize = this.calculateFrameSize()
    const { width, height } = frameSize
    const length = this.config.coneLength
    const halfWidth = width / 2
    const halfHeight = height / 2
    // 相机世界矩阵
    const m = new THREE.Matrix4()
    m.copy(this.camera.matrixWorld)
    // 视锥底面4点（相机空间->世界空间）
    const p1 = new THREE.Vector3(-halfWidth, -halfHeight, -length).applyMatrix4(m)
    const p2 = new THREE.Vector3(halfWidth, -halfHeight, -length).applyMatrix4(m)
    const p3 = new THREE.Vector3(halfWidth, halfHeight, -length).applyMatrix4(m)
    const p4 = new THREE.Vector3(-halfWidth, halfHeight, -length).applyMatrix4(m)
    const camPos = new THREE.Vector3().setFromMatrixPosition(m)
    // 4个平面：每个平面由相机点和底面相邻两点确定
    const planes = [
      new THREE.Plane().setFromCoplanarPoints(camPos, p1, p2), // 下
      new THREE.Plane().setFromCoplanarPoints(camPos, p2, p3), // 右
      new THREE.Plane().setFromCoplanarPoints(camPos, p3, p4), // 上
      new THREE.Plane().setFromCoplanarPoints(camPos, p4, p1)  // 左
    ]
    // 保证法线朝内
    for (let i = 0; i < planes.length; i++) {
      if (planes[i].distanceToPoint(camPos.clone().add(planes[i].normal)) < planes[i].distanceToPoint(camPos)) {
        planes[i].negate()
      }
    }
    this.clippingPlanes = planes
    this.renderer.clippingPlanes = planes
  }

  private createViewportConeAndLight() {
    // 清理旧的视锥和聚光灯
    if (this.viewportCone) {
      this.coneGroup.remove(this.viewportCone)
      this.viewportCone.geometry.dispose()
      if (Array.isArray(this.viewportCone.material)) {
        this.viewportCone.material.forEach(mat => mat.dispose())
      } else {
        this.viewportCone.material.dispose()
      }
      this.viewportCone = null
    }
    if (this.spotLight) {
      this.coneGroup.remove(this.spotLight)
      this.spotLight = null
    }
    if (this.spotLightTarget) {
      this.coneGroup.remove(this.spotLightTarget)
      this.spotLightTarget = null
    }
    // 计算视锥的四个底面顶点（基于蒙板尺寸）
    const frameSize = this.calculateFrameSize()
    const { width, height } = frameSize
    const length = this.config.coneLength
    // 创建视锥Mesh
    const coneGeometry = this.createConeGeometry(width, height, length)
    const coneMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x000000, // 视锥颜色：纯黑色
      opacity: this.config.coneOpacity,
      transparent: true,
      side: THREE.DoubleSide,
      depthWrite: false,
      depthTest: true,
      roughness: 0.5,
      metalness: 0.1,
      transmission: this.config.coneOpacity < 1 ? this.config.coneOpacity : 0
    })
    this.viewportCone = new THREE.Mesh(coneGeometry, coneMaterial)
    this.viewportCone.renderOrder = 1
    this.coneGroup.add(this.viewportCone)
    // 创建聚光灯
    this.spotLight = new THREE.SpotLight(0xff0000, 100, 100, Math.atan(width / (2 * length)) * 2, 0.2, 0)
    this.spotLight.castShadow = false
    this.spotLight.position.set(0, 0, 0)
    // 聚光灯target
    this.spotLightTarget = new THREE.Object3D()
    this.spotLightTarget.position.set(0, 0, -length)
    this.coneGroup.add(this.spotLightTarget)
    this.spotLight.target = this.spotLightTarget
    this.coneGroup.add(this.spotLight)
  }

  // 清理资源
  dispose(): void {
    // 清理框架组
    if (this.frameGroup) {
      this.scene.remove(this.frameGroup)
      this.frameGroup.clear()
    }

    // 清理视锥组
    if (this.coneGroup) {
      this.scene.remove(this.coneGroup)
      this.coneGroup.clear()
    }

    // 清理蒙板平面
    this.maskPlanes.forEach(plane => {
      if (plane.geometry) plane.geometry.dispose()
      if (plane.material) {
        if (Array.isArray(plane.material)) {
          plane.material.forEach(mat => mat.dispose())
        } else {
          plane.material.dispose()
        }
      }
    })
    this.maskPlanes = []

    // 清理视野框架
    if (this.viewportFrame) {
      if (this.viewportFrame.geometry) this.viewportFrame.geometry.dispose()
      if (this.viewportFrame.material) {
        if (Array.isArray(this.viewportFrame.material)) {
          this.viewportFrame.material.forEach(mat => mat.dispose())
        } else {
          this.viewportFrame.material.dispose()
        }
      }
      this.viewportFrame = null
    }

    // 清理视锥
    if (this.viewportCone) {
      if (this.viewportCone.geometry) this.viewportCone.geometry.dispose()
      if (this.viewportCone.material) {
        if (Array.isArray(this.viewportCone.material)) {
          this.viewportCone.material.forEach(mat => mat.dispose())
        } else {
          this.viewportCone.material.dispose()
        }
      }
      this.viewportCone = null
    }

    // 清理聚光灯
    if (this.spotLight) {
      this.spotLight = null
    }
    if (this.spotLightTarget) {
      this.spotLightTarget = null
    }
  }
}
