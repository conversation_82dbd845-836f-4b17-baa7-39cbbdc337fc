# 测试电文示例

## 基本移动测试

### 1. 移动到指定位置
```json
{"vehicleId": "car1", "action": "move", "x": 10, "y": 0, "z": 5, "speed": 2}
```

### 2. 向前移动
```json
{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 8, "speed": 3}
```

### 3. 向后移动
```json
{"vehicleId": "car1", "action": "move", "direction": "backward", "distance": 5, "speed": 2}
```

### 4. 左转
```json
{"vehicleId": "car1", "action": "turn", "direction": "left", "angle": 45, "speed": 1}
```

### 5. 右转
```json
{"vehicleId": "car1", "action": "turn", "direction": "right", "angle": 90, "speed": 1.5}
```

### 6. 重置位置
```json
{"vehicleId": "car1", "action": "reset"}
```

## 履带控制测试

**注意：履带会以自己的几何中心为轴进行Y轴旋转，系统会自动计算每个履带的中心点**

### 7. 启动所有履带旋转
```json
{"vehicleId": "car1", "action": "track", "trackAction": "start", "trackNames": ["履带1", "履带2", "履带3"], "rotationSpeed": 2}
```

### 8. 停止履带旋转
```json
{"vehicleId": "car1", "action": "track", "trackAction": "stop"}
```

### 9. 设置履带速度（运行时调整）
```json
{"vehicleId": "car1", "action": "track", "trackAction": "setSpeed", "rotationSpeed": 3}
```

### 10. 启动单个履带（测试独立控制）
```json
{"vehicleId": "car1", "action": "track", "trackAction": "start", "trackNames": ["履带1"], "rotationSpeed": 1.5}
```

### 11. 启动指定履带组合（差速控制）
```json
{"vehicleId": "car1", "action": "track", "trackAction": "start", "trackNames": ["履带1", "履带3"], "rotationSpeed": 2.5}
```

### 12. 慢速精确控制
```json
{"vehicleId": "car1", "action": "track", "trackAction": "start", "trackNames": ["履带1", "履带2", "履带3"], "rotationSpeed": 0.5}
```

### 13. 高速旋转测试
```json
{"vehicleId": "car1", "action": "track", "trackAction": "start", "trackNames": ["履带1", "履带2", "履带3"], "rotationSpeed": 5}
```

## 复杂移动序列

### 绕圈移动
1. 向前移动：`{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 10, "speed": 2}`
2. 右转90度：`{"vehicleId": "car1", "action": "turn", "direction": "right", "angle": 90, "speed": 1}`
3. 向前移动：`{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 10, "speed": 2}`
4. 右转90度：`{"vehicleId": "car1", "action": "turn", "direction": "right", "angle": 90, "speed": 1}`
5. 向前移动：`{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 10, "speed": 2}`
6. 右转90度：`{"vehicleId": "car1", "action": "turn", "direction": "right", "angle": 90, "speed": 1}`
7. 向前移动：`{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 10, "speed": 2}`
8. 右转90度：`{"vehicleId": "car1", "action": "turn", "direction": "right", "angle": 90, "speed": 1}`

### 8字形移动
1. 移动到起点：`{"vehicleId": "car1", "x": -5, "y": 0, "z": 0, "speed": 2}`
2. 向右移动：`{"vehicleId": "car1", "x": 5, "y": 0, "z": 0, "speed": 2}`
3. 向上移动：`{"vehicleId": "car1", "x": 5, "y": 0, "z": 5, "speed": 2}`
4. 向左移动：`{"vehicleId": "car1", "x": -5, "y": 0, "z": 5, "speed": 2}`
5. 回到中心：`{"vehicleId": "car1", "x": 0, "y": 0, "z": 2.5, "speed": 2}`
6. 向右下：`{"vehicleId": "car1", "x": 5, "y": 0, "z": -5, "speed": 2}`
7. 向左移动：`{"vehicleId": "car1", "x": -5, "y": 0, "z": -5, "speed": 2}`
8. 回到原点：`{"vehicleId": "car1", "x": 0, "y": 0, "z": 0, "speed": 2}`

## 直行拐弯演示场景

### 完整的矿山布料机操作演示
点击"直行拐弯演示"按钮，或按顺序发送以下电文：

1. **重置位置**：`{"vehicleId": "car1", "action": "reset"}`
2. **启动履带**：`{"vehicleId": "car1", "action": "track", "trackAction": "start", "trackNames": ["履带1", "履带2", "履带3"], "rotationSpeed": 2}`
3. **直行10米**：`{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 10, "speed": 3}`
4. **右转90度**：`{"vehicleId": "car1", "action": "turn", "direction": "right", "angle": 90, "speed": 1}`
5. **继续直行8米**：`{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 8, "speed": 3}`
6. **左转45度**：`{"vehicleId": "car1", "action": "turn", "direction": "left", "angle": 45, "speed": 1}`
7. **最终直行5米**：`{"vehicleId": "car1", "action": "move", "direction": "forward", "distance": 5, "speed": 2}`
8. **停止履带**：`{"vehicleId": "car1", "action": "track", "trackAction": "stop"}`

## 使用说明

1. **自动演示**：点击"直行拐弯演示"按钮体验完整场景
2. **手动控制**：复制上述JSON电文到控制面板的电文输入框
3. **发送电文**：点击"发送电文"按钮
4. **观察效果**：在3D场景中观察车辆移动和履带旋转
5. **预设按钮**：使用预设按钮进行快速测试

## 注意事项

- 所有坐标以场景中心为原点
- Y坐标通常保持为0（地面高度）
- 速度值越大移动越快
- 角度以度为单位
- 车辆移动是异步的，等待当前动作完成后再发送下一个电文
